#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的缩放同步测试脚本

直接测试渲染器的缩放功能，避免复杂的依赖
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt, QRect
from PyQt5.QtGui import QPainter, QColor, QPen

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from src.ui.point_figure.renderers.grid_renderer import GridRenderer
    from src.ui.point_figure.renderers.symbol_renderer import SymbolRenderer
    RENDERERS_AVAILABLE = True
except ImportError as e:
    print(f"渲染器导入失败: {e}")
    RENDERERS_AVAILABLE = False


class SimpleZoomTestWidget(QWidget):
    """简化的缩放测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(800, 600)
        
        # 测试参数
        self.base_cell_width = 20
        self.base_cell_height = 20
        self.zoom_factor = 1.0
        
        # 创建渲染器（如果可用）
        if RENDERERS_AVAILABLE:
            self.grid_renderer = GridRenderer()
            self.symbol_renderer = SymbolRenderer()
        
        # 测试数据
        self.test_chart_model = self._create_test_data()
    
    def _create_test_data(self):
        """创建测试数据"""
        import pandas as pd
        import numpy as np
        
        # 创建简单的测试数据
        prices = [10.0, 10.5, 11.0, 11.5, 12.0, 12.5, 13.0]
        columns = [0, 1, 2, 3, 4]
        
        # 创建DataFrame
        chart_data = pd.DataFrame(index=prices, columns=columns)
        
        # 填充一些测试符号
        chart_data.loc[10.5, 0] = 'X'
        chart_data.loc[11.0, 0] = 'X'
        chart_data.loc[11.5, 1] = 'O'
        chart_data.loc[12.0, 1] = 'O'
        chart_data.loc[12.5, 2] = 'X'
        chart_data.loc[13.0, 2] = 'X'
        chart_data.loc[11.0, 3] = 'O'
        chart_data.loc[10.5, 4] = 'X'
        
        return {
            'chart_data': chart_data,
            'price_data': prices,
            'column_data': columns,
            'date_labels': [f"Col {i}" for i in columns]
        }
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 应用缩放变换
        painter.scale(self.zoom_factor, self.zoom_factor)
        
        # 计算当前单元格尺寸
        current_cell_width = self.base_cell_width
        current_cell_height = self.base_cell_height
        
        # 绘制背景
        painter.fillRect(self.rect(), QColor(240, 240, 240))
        
        if RENDERERS_AVAILABLE:
            # 使用渲染器绘制
            rect = QRect(0, 0, 800, 600)
            
            try:
                # 绘制网格
                self.grid_renderer.render(painter, rect, self.test_chart_model, 
                                        current_cell_width, current_cell_height)
                
                # 绘制符号
                self.symbol_renderer.render(painter, rect, self.test_chart_model,
                                          current_cell_width, current_cell_height)
            except Exception as e:
                print(f"渲染错误: {e}")
        else:
            # 手动绘制简单的测试图形
            self._draw_simple_test(painter, current_cell_width, current_cell_height)
    
    def _draw_simple_test(self, painter, cell_width, cell_height):
        """手动绘制简单的测试图形"""
        # 绘制网格线
        pen = QPen(QColor(200, 200, 200))
        pen.setWidth(max(1, int(1 * min(cell_width/20, cell_height/20))))
        painter.setPen(pen)
        
        # 绘制垂直线
        for i in range(6):
            x = int(i * cell_width)
            painter.drawLine(x, 0, x, int(7 * cell_height))

        # 绘制水平线
        for i in range(8):
            y = int(i * cell_height)
            painter.drawLine(0, y, int(5 * cell_width), y)
        
        # 绘制一些测试符号
        pen = QPen(QColor(255, 0, 0))
        pen.setWidth(max(1, int(2 * min(cell_width/20, cell_height/20))))
        painter.setPen(pen)

        # 绘制X符号
        x_size = int(cell_width * 0.6)
        y_size = int(cell_height * 0.6)

        for i in range(3):
            center_x = int(i * cell_width + cell_width // 2)
            center_y = int((i + 1) * cell_height + cell_height // 2)

            # 绘制X
            painter.drawLine(center_x - x_size//2, center_y - y_size//2,
                           center_x + x_size//2, center_y + y_size//2)
            painter.drawLine(center_x + x_size//2, center_y - y_size//2,
                           center_x - x_size//2, center_y + y_size//2)

        # 绘制O符号
        pen.setColor(QColor(0, 0, 255))
        painter.setPen(pen)

        for i in range(2):
            center_x = int((i + 3) * cell_width + cell_width // 2)
            center_y = int((i + 2) * cell_height + cell_height // 2)

            # 绘制O
            painter.drawEllipse(center_x - x_size//2, center_y - y_size//2,
                              x_size, y_size)
    
    def set_zoom(self, zoom):
        """设置缩放级别"""
        self.zoom_factor = zoom
        self.update()


class SimpleZoomTestWindow(QMainWindow):
    """简化的缩放测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("威科夫点数图缩放同步测试 (简化版)")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        control_panel = self._create_control_panel()
        layout.addWidget(control_panel)
        
        # 创建测试控件
        self.test_widget = SimpleZoomTestWidget()
        layout.addWidget(self.test_widget)
        
        # 初始缩放
        self.current_zoom = 1.0
        self._update_zoom_display()
    
    def _create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # 缩放控制按钮
        zoom_out_btn = QPushButton("缩小 (0.8x)")
        zoom_out_btn.clicked.connect(lambda: self._apply_zoom(0.8))
        layout.addWidget(zoom_out_btn)
        
        zoom_in_btn = QPushButton("放大 (1.25x)")
        zoom_in_btn.clicked.connect(lambda: self._apply_zoom(1.25))
        layout.addWidget(zoom_in_btn)
        
        reset_btn = QPushButton("重置缩放")
        reset_btn.clicked.connect(self._reset_zoom)
        layout.addWidget(reset_btn)
        
        # 缩放显示标签
        self.zoom_label = QLabel("当前缩放: 100%")
        layout.addWidget(self.zoom_label)
        
        layout.addStretch()
        
        # 状态显示
        status = "渲染器可用" if RENDERERS_AVAILABLE else "使用简化绘制"
        status_label = QLabel(f"状态: {status}")
        status_label.setStyleSheet("color: green; font-weight: bold;" if RENDERERS_AVAILABLE 
                                 else "color: orange; font-weight: bold;")
        layout.addWidget(status_label)
        
        return panel
    
    def _apply_zoom(self, factor):
        """应用缩放"""
        self.current_zoom *= factor
        self.current_zoom = max(0.1, min(5.0, self.current_zoom))
        
        self.test_widget.set_zoom(self.current_zoom)
        self._update_zoom_display()
        
        print(f"应用缩放: {self.current_zoom:.2f}x ({self.current_zoom*100:.0f}%)")
    
    def _reset_zoom(self):
        """重置缩放"""
        self.current_zoom = 1.0
        self.test_widget.set_zoom(1.0)
        self._update_zoom_display()
        print("重置缩放到100%")
    
    def _update_zoom_display(self):
        """更新缩放显示"""
        self.zoom_label.setText(f"当前缩放: {self.current_zoom*100:.0f}%")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = SimpleZoomTestWindow()
    window.show()
    
    print("威科夫点数图缩放同步测试启动 (简化版)")
    print("=" * 50)
    print("测试说明:")
    print("1. 点击'放大'和'缩小'按钮测试缩放功能")
    print("2. 观察网格线和符号是否同步缩放")
    print("3. 检查线宽是否随缩放调整")
    if not RENDERERS_AVAILABLE:
        print("注意: 渲染器不可用，使用简化绘制进行测试")
    print("=" * 50)
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
