#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关于对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QLabel, QPushButton,
                            QHBoxLayout, QTabWidget, QWidget, QTextBrowser)
from PyQt5.QtGui import QPixmap, QFont
from PyQt5.QtCore import Qt

# 导入版本号
import src
__version__ = src.__version__


class AboutDialog(QDialog):
    """关于对话框类"""
    
    def __init__(self, parent=None):
        """初始化关于对话框"""
        super().__init__(parent)
        
        # 初始化界面
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("关于")
        self.setFixedSize(500, 400)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 创建关于标签页
        about_tab = QWidget()
        about_layout = QVBoxLayout(about_tab)
        
        # 创建标题标签,显示软件名称和联系方式
        title_label = QLabel("威科夫点数图软件\n软件功能支持定制，联系微信：zxzvsdcj")
        title_label.setWordWrap(True)
        
        # 创建字体对象并设置字体属性
        title_font = QFont()
        title_font.setPointSize(16)  # 设置字体大小为16点
        title_font.setBold(True)     # 设置字体为粗体
        
        # 将字体应用到标题标签
        title_label.setFont(title_font)
        
        # 设置标题文本居中对齐
        title_label.setAlignment(Qt.AlignCenter)
        
        # 将标题标签添加到关于页面的布局中
        about_layout.addWidget(title_label)
        
        # 添加版本信息
        version_label = QLabel(f"版本 {__version__}")
        version_label.setAlignment(Qt.AlignCenter)
        about_layout.addWidget(version_label)
        
        # 添加描述
        description_label = QLabel(
            "威科夫点数图软件是一款专业的点数图绘制工具，"
            "支持多种绘制方法和自定义设置，"
            "帮助投资者更好地分析市场趋势。"
        )
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignCenter)
        about_layout.addWidget(description_label)
        
        # 添加版权信息
        copyright_label = QLabel("© 2023-2024 威科夫点数图软件团队")
        copyright_label.setAlignment(Qt.AlignCenter)
        about_layout.addWidget(copyright_label)
        
        # 添加空白区域
        about_layout.addStretch()
        
        # 添加关于标签页
        tab_widget.addTab(about_tab, "关于")
        
        # 创建许可标签页
        license_tab = QWidget()
        license_layout = QVBoxLayout(license_tab)
        
        # 添加许可文本
        license_browser = QTextBrowser()
        license_browser.setHtml("""
        <h3>开源软件许可</h3>
        <p>本软件使用以下开源组件：</p>
        <ul>
            <li>Python - PSF License</li>
            <li>PyQt5 - GPL v3</li>
            <li>NumPy - BSD License</li>
            <li>Pandas - BSD License</li>
        </ul>
        <p>本软件遵循 GPL v3 许可证发布。</p>
        """)
        license_layout.addWidget(license_browser)
        
        # 添加许可标签页
        tab_widget.addTab(license_tab, "许可")
        
        # 添加标签页到主布局
        main_layout.addWidget(tab_widget)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 添加确定按钮
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept)
        
        # 添加按钮到布局
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        
        # 添加按钮布局到主布局
        main_layout.addLayout(button_layout) 