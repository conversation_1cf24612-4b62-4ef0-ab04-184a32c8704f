#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图生成器模型

用于根据价格数据生成点数图数据
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime

from src.core.point_figure.data_structures import ChartSymbol, ChartColumn, PointFigureChartData


class PointFigureChartGenerator:
    """点数图生成器类，用于生成点数图数据"""

    def __init__(self):
        """初始化点数图生成器"""
        self._data = None
        self._box_size = 0.0
        self._reversal_amount = 0

    def set_parameters(self, box_size: float, reversal_amount: int) -> None:
        """
        设置点数图参数

        参数:
            box_size: float - 格子大小（价格变动最小单位）
            reversal_amount: int - 反转格数（反转所需的最小格子数量）
        """
        self._box_size = box_size
        self._reversal_amount = reversal_amount

    def generate_point_figure_chart(
        self,
        data: pd.DataFrame,
        method: str = "hl",
        box_size: Optional[float] = None,
        reversal_amount: Optional[int] = None
    ) -> PointFigureChartData:
        """
        生成点数图数据

        参数:
            data: pd.DataFrame - 价格数据，应包含日期索引和OHLCV列
            method: str - 生成方法，可以是 "hl"(高低) 或 "close"(收盘价)
            box_size: float - 格子大小，如果为None则使用已设置的值
            reversal_amount: int - 反转格数，如果为None则使用已设置的值

        返回:
            PointFigureChartData - 生成的点数图数据对象
        """
        if data is None or data.empty:
            raise ValueError("输入数据不能为空")

        self._data = data.copy()

        # 如果有提供参数，则更新当前参数
        if box_size is not None:
            self._box_size = box_size
        if reversal_amount is not None:
            self._reversal_amount = reversal_amount

        # 参数检查
        if self._box_size <= 0:
            raise ValueError("格子大小必须大于0")
        if self._reversal_amount <= 0:
            raise ValueError("反转格数必须大于0")

        # 根据方法调用相应的生成函数
        if method.lower() == "hl":
            return self._generate_high_low_chart()
        elif method.lower() == "close":
            return self._generate_close_chart()
        else:
            raise ValueError(f"不支持的生成方法: {method}")

    def _generate_high_low_chart(self) -> PointFigureChartData:
        """
        使用高低价生成点数图

        返回:
            PointFigureChartData - 生成的点数图数据对象
        """
        if "high" not in self._data.columns or "low" not in self._data.columns:
            raise ValueError("数据必须包含high和low列")

        data = self._data.sort_index()

        # 初始化变量
        current_direction = None  # 当前方向: "X" 或 "O"
        current_price = None  # 当前价格
        columns = []  # 列数据
        current_column = None  # 当前列
        col_index = 0  # 列索引

        for date, row in data.iterrows():
            date_str = date.strftime("%Y-%m-%d") if isinstance(date, datetime) else str(date)
            high = row["high"]
            low = row["low"]
            volume = row.get("volume", 0)

            # 第一个数据点初始化
            if current_direction is None:
                # 从第一个点开始，假设为上涨
                current_direction = "X"
                current_price = low
                current_column = ChartColumn(symbols=[], index=col_index)

                # 根据第一个价格格子化
                grid_price = self._box_size * round(current_price / self._box_size)

                # 添加第一个符号
                for p in self._get_prices_in_range(grid_price, high):
                    symbol = ChartSymbol(
                        type="X",
                        price=p,
                        date=date_str,
                        volume=volume
                    )
                    current_column.symbols.append(symbol)

                columns.append(current_column)
                current_price = current_column.symbols[-1].price if current_column.symbols else grid_price
                continue

            # 处理后续数据点
            if current_direction == "X":
                # 当前是上涨趋势

                # 检查是否突破新高
                potential_high = self._box_size * (round(high / self._box_size) + 0.5)
                if potential_high > current_price + self._box_size/2:
                    # 继续上涨，添加新的X
                    for p in self._get_prices_in_range(current_price + self._box_size, potential_high):
                        symbol = ChartSymbol(
                            type="X",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price

                # 检查是否需要反转
                potential_low = self._box_size * (round(low / self._box_size) - 0.5)
                if current_price - potential_low >= self._box_size * self._reversal_amount:
                    # 反转为下跌趋势
                    current_direction = "O"
                    col_index += 1
                    current_column = ChartColumn(symbols=[], index=col_index)
                    columns.append(current_column)

                    # 添加O符号
                    for p in self._get_prices_in_range(current_price - self._box_size, potential_low, -1):
                        symbol = ChartSymbol(
                            type="O",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price if current_column.symbols else potential_low

            else:  # current_direction == "O"
                # 当前是下跌趋势

                # 检查是否突破新低
                potential_low = self._box_size * (round(low / self._box_size) - 0.5)
                if potential_low < current_price - self._box_size/2:
                    # 继续下跌，添加新的O
                    for p in self._get_prices_in_range(current_price - self._box_size, potential_low, -1):
                        symbol = ChartSymbol(
                            type="O",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price

                # 检查是否需要反转
                potential_high = self._box_size * (round(high / self._box_size) + 0.5)
                if potential_high - current_price >= self._box_size * self._reversal_amount:
                    # 反转为上涨趋势
                    current_direction = "X"
                    col_index += 1
                    current_column = ChartColumn(symbols=[], index=col_index)
                    columns.append(current_column)

                    # 添加X符号
                    for p in self._get_prices_in_range(current_price + self._box_size, potential_high):
                        symbol = ChartSymbol(
                            type="X",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price if current_column.symbols else potential_high

        # 创建并返回点数图数据对象
        chart_data = PointFigureChartData(
            columns=columns,
            box_size=self._box_size,
            reversal_amount=self._reversal_amount
        )

        # 预先扩展价格范围以支持目标位计算
        chart_data = self._expand_price_range_for_targets(chart_data)

        return chart_data

    def _generate_close_chart(self) -> PointFigureChartData:
        """
        使用收盘价生成点数图

        返回:
            PointFigureChartData - 生成的点数图数据对象
        """
        if "close" not in self._data.columns:
            raise ValueError("数据必须包含close列")

        data = self._data.sort_index()

        # 初始化变量
        current_direction = None  # 当前方向: "X" 或 "O"
        current_price = None  # 当前价格
        columns = []  # 列数据
        current_column = None  # 当前列
        col_index = 0  # 列索引

        for date, row in data.iterrows():
            date_str = date.strftime("%Y-%m-%d") if isinstance(date, datetime) else str(date)
            close = row["close"]
            volume = row.get("volume", 0)

            # 第一个数据点初始化
            if current_direction is None:
                # 从第一个点开始，假设为上涨
                current_direction = "X"
                current_price = close
                current_column = ChartColumn(symbols=[], index=col_index)

                # 根据第一个价格格子化
                grid_price = self._box_size * round(current_price / self._box_size)

                # 添加第一个符号
                symbol = ChartSymbol(
                    type="X",
                    price=grid_price,
                    date=date_str,
                    volume=volume
                )
                current_column.symbols.append(symbol)

                columns.append(current_column)
                current_price = grid_price
                continue

            # 处理后续数据点
            grid_close = self._box_size * round(close / self._box_size)

            if current_direction == "X":
                # 当前是上涨趋势

                # 检查是否突破新高
                if grid_close > current_price:
                    # 继续上涨，添加新的X
                    for p in self._get_prices_in_range(current_price + self._box_size, grid_close):
                        symbol = ChartSymbol(
                            type="X",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price

                # 检查是否需要反转
                elif current_price - grid_close >= self._box_size * self._reversal_amount:
                    # 反转为下跌趋势
                    current_direction = "O"
                    col_index += 1
                    current_column = ChartColumn(symbols=[], index=col_index)
                    columns.append(current_column)

                    # 添加O符号
                    for p in self._get_prices_in_range(current_price - self._box_size, grid_close, -1):
                        symbol = ChartSymbol(
                            type="O",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price if current_column.symbols else grid_close

            else:  # current_direction == "O"
                # 当前是下跌趋势

                # 检查是否突破新低
                if grid_close < current_price:
                    # 继续下跌，添加新的O
                    for p in self._get_prices_in_range(current_price - self._box_size, grid_close, -1):
                        symbol = ChartSymbol(
                            type="O",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price

                # 检查是否需要反转
                elif grid_close - current_price >= self._box_size * self._reversal_amount:
                    # 反转为上涨趋势
                    current_direction = "X"
                    col_index += 1
                    current_column = ChartColumn(symbols=[], index=col_index)
                    columns.append(current_column)

                    # 添加X符号
                    for p in self._get_prices_in_range(current_price + self._box_size, grid_close):
                        symbol = ChartSymbol(
                            type="X",
                            price=p,
                            date=date_str,
                            volume=volume
                        )
                        current_column.symbols.append(symbol)
                    current_price = current_column.symbols[-1].price if current_column.symbols else grid_close

        # 创建并返回点数图数据对象
        chart_data = PointFigureChartData(
            columns=columns,
            box_size=self._box_size,
            reversal_amount=self._reversal_amount
        )

        # 预先扩展价格范围以支持目标位计算
        chart_data = self._expand_price_range_for_targets(chart_data)

        return chart_data

    def _expand_price_range_for_targets(self, chart_data: PointFigureChartData) -> PointFigureChartData:
        """
        预先扩展价格范围以支持目标位计算，避免在测量时动态扩展造成卡顿

        参数:
            chart_data: 原始点数图数据

        返回:
            扩展后的点数图数据
        """
        try:
            if not chart_data.columns:
                return chart_data

            # 获取设置管理器
            from utils.settings_manager import SettingsManager
            settings = SettingsManager()

            # 从设置中获取预扩展参数
            expand_up_rows = settings.get_setting("table_expansion", "expand_up_rows", 10)
            expand_down_rows = settings.get_setting("table_expansion", "expand_down_rows", 10)

            # 获取当前价格范围
            all_prices = []
            for column in chart_data.columns:
                for symbol in column.symbols:
                    all_prices.append(symbol.price)

            if not all_prices:
                return chart_data

            current_min = min(all_prices)
            current_max = max(all_prices)

            # 根据设置计算扩展范围
            up_extension = expand_up_rows * self._box_size
            down_extension = expand_down_rows * self._box_size

            # 计算新的价格范围
            new_min = current_min - down_extension
            new_max = current_max + up_extension

            # 将价格对齐到格子边界
            new_min = self._box_size * round(new_min / self._box_size)
            new_max = self._box_size * round(new_max / self._box_size)

            print(f"预扩展价格范围: {current_min:.2f}-{current_max:.2f} -> {new_min:.2f}-{new_max:.2f}")
            print(f"扩展参数: 向上{expand_up_rows}行, 向下{expand_down_rows}行")

            # 创建扩展的价格序列
            extended_prices = []
            price = new_min
            while price <= new_max:
                extended_prices.append(price)
                price += self._box_size

            # 创建新的图表数据结构，保持原有列数据不变
            # 这里只是预留了价格空间，实际的表格扩展在UI层处理
            chart_data._extended_price_range = (new_min, new_max)
            chart_data._extended_prices = extended_prices

            return chart_data

        except Exception as e:
            print(f"扩展价格范围出错: {str(e)}")
            return chart_data

    def _get_prices_in_range(self, start: float, end: float, step_direction: int = 1) -> List[float]:
        """
        获取指定范围内的价格序列

        参数:
            start: float - 起始价格
            end: float - 结束价格
            step_direction: int - 步进方向，1表示上涨，-1表示下跌

        返回:
            List[float] - 价格序列
        """
        if step_direction > 0:
            if start > end:
                return []

            prices = []
            current = start
            while current <= end + 1e-10:  # 添加小误差避免浮点数比较问题
                prices.append(current)
                current += self._box_size

            return prices
        else:
            if start < end:
                return []

            prices = []
            current = start
            while current >= end - 1e-10:  # 添加小误差避免浮点数比较问题
                prices.append(current)
                current -= self._box_size

            return prices