#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试小数格值的价格对齐功能
专门测试1.5、2.5、3.5等小数格值
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

from models.point_figure_model import PointFigureModel

def test_decimal_box_sizes():
    """测试小数格值的价格对齐功能"""
    print("=== 测试小数格值的价格对齐功能 ===")
    
    model = PointFigureModel()
    
    # 测试不同的小数格值
    test_cases = [
        # (原价格, 格值, 期望结果)
        (10.0, 1.5, 10.5),   # 10.0 应该对齐到 10.5 (7*1.5)
        (11.0, 1.5, 10.5),   # 11.0 应该对齐到 10.5 (7*1.5)
        (12.0, 1.5, 12.0),   # 12.0 应该对齐到 12.0 (8*1.5)
        (13.0, 1.5, 13.5),   # 13.0 应该对齐到 13.5 (9*1.5)
        
        (10.0, 2.5, 10.0),   # 10.0 应该对齐到 10.0 (4*2.5)
        (11.0, 2.5, 10.0),   # 11.0 应该对齐到 10.0 (4*2.5)
        (12.0, 2.5, 12.5),   # 12.0 应该对齐到 12.5 (5*2.5)
        (13.0, 2.5, 12.5),   # 13.0 应该对齐到 12.5 (5*2.5)
        
        (10.0, 3.5, 10.5),   # 10.0 应该对齐到 10.5 (3*3.5)
        (12.0, 3.5, 10.5),   # 12.0 应该对齐到 10.5 (3*3.5)
        (14.0, 3.5, 14.0),   # 14.0 应该对齐到 14.0 (4*3.5)
        (15.0, 3.5, 14.0),   # 15.0 应该对齐到 14.0 (4*3.5)
    ]
    
    for original_price, box_size, expected in test_cases:
        aligned_price = model._align_price_to_box_size(original_price, box_size)
        ratio = aligned_price / box_size
        is_multiple = abs(ratio - round(ratio)) < 1e-10
        
        print(f"原价格: {original_price}, 格值: {box_size}, 对齐后: {aligned_price}")
        print(f"  验证: {aligned_price} / {box_size} = {ratio:.10f} (是整数倍: {is_multiple})")
        print(f"  期望: {expected}, 实际: {aligned_price}, 匹配: {abs(aligned_price - expected) < 1e-10}")
        print()

def test_decimal_price_sequence():
    """测试小数格值的价格序列生成"""
    print("=== 测试小数格值的价格序列生成 ===")
    
    model = PointFigureModel()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'high': [12.5, 13.0],
        'low': [12.0, 12.5],
        'volume': [1000, 1500]
    })
    
    # 测试不同的小数格值
    box_sizes = [1.5, 2.5, 3.5]
    
    for box_size in box_sizes:
        print(f"\n--- 格值: {box_size} ---")
        
        try:
            # 生成点数图
            result = model._convert_to_dataframe([], box_size=box_size)
            
            if result is not None and not result.empty:
                prices = result.index.tolist()
                print(f"价格数量: {len(prices)}")
                if prices:
                    print(f"价格范围: {min(prices):.3f} - {max(prices):.3f}")
                    print(f"前5个价格: {[f'{p:.3f}' for p in prices[:5]]}")
                    if len(prices) > 5:
                        print(f"后5个价格: {[f'{p:.3f}' for p in prices[-5:]]}")
                    
                    # 验证所有价格都是格值的整数倍
                    invalid_count = 0
                    for price in prices:
                        ratio = price / box_size
                        if abs(ratio - round(ratio)) > 1e-10:
                            invalid_count += 1
                    
                    if invalid_count == 0:
                        print(f"  ✓ 所有价格都是格值 {box_size} 的整数倍")
                    else:
                        print(f"  ✗ 发现 {invalid_count} 个不是格值整数倍的价格")
                    
                    # 检查价格序列的连续性
                    if len(prices) > 1:
                        intervals = []
                        for i in range(len(prices) - 1):
                            interval = abs(prices[i] - prices[i+1])
                            intervals.append(interval)
                        
                        # 检查间隔是否都等于格值
                        expected_interval = box_size
                        all_correct = all(abs(interval - expected_interval) < 1e-10 for interval in intervals)
                        
                        if all_correct:
                            print(f"  ✓ 价格序列连续，间隔均为格值 {box_size}")
                        else:
                            print(f"  ✗ 价格序列间隔不一致")
                            unique_intervals = list(set(round(interval, 10) for interval in intervals))
                            print(f"    发现的间隔: {unique_intervals}")
            else:
                print("  生成的DataFrame为空")
                
        except Exception as e:
            print(f"  错误: {str(e)}")

def test_actual_chart_generation():
    """测试实际的点数图生成"""
    print("\n=== 测试实际点数图生成 ===")
    
    model = PointFigureModel()
    
    # 创建更完整的测试数据
    test_data = pd.DataFrame({
        'high': [10.0, 11.5, 13.0, 12.0, 14.5, 16.0, 15.5, 17.0],
        'low': [9.5, 10.0, 11.5, 11.0, 12.5, 14.0, 14.5, 15.0],
        'volume': [1000, 1200, 1500, 1100, 1800, 2000, 1600, 1900]
    })
    
    # 测试小数格值
    box_sizes = [1.5, 2.5]
    reversal_amount = 3
    
    for box_size in box_sizes:
        print(f"\n--- 测试格值 {box_size} 的点数图生成 ---")
        
        try:
            result = model.generate_point_figure_chart(
                test_data, 
                box_size=box_size, 
                reversal_amount=reversal_amount, 
                method="high_low"
            )
            
            if result is not None and not result.empty:
                prices = result.index.tolist()
                print(f"生成的价格数量: {len(prices)}")
                print(f"价格范围: {min(prices):.3f} - {max(prices):.3f}")
                
                # 显示前10个价格
                print(f"前10个价格: {[f'{p:.3f}' for p in prices[:10]]}")
                
                # 验证价格对齐
                invalid_prices = []
                for price in prices:
                    ratio = price / box_size
                    if abs(ratio - round(ratio)) > 1e-10:
                        invalid_prices.append(price)
                
                if not invalid_prices:
                    print(f"  ✓ 所有 {len(prices)} 个价格都是格值 {box_size} 的整数倍")
                else:
                    print(f"  ✗ 发现 {len(invalid_prices)} 个不是格值整数倍的价格:")
                    for price in invalid_prices[:5]:  # 只显示前5个
                        print(f"    价格: {price:.6f}, 比值: {price/box_size:.6f}")
                
                # 显示实际的点数图内容（前几列）
                print("\n  点数图内容预览:")
                for i, price in enumerate(prices[:15]):  # 只显示前15行
                    row_content = []
                    for col in range(min(5, len(result.columns))):  # 只显示前5列
                        symbol = result.iloc[i, col]
                        row_content.append(symbol if pd.notna(symbol) else ' ')
                    print(f"    {price:6.1f}: {' '.join(row_content)}")
                    
            else:
                print("  生成的点数图为空")
                
        except Exception as e:
            print(f"  错误: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_decimal_box_sizes()
    test_decimal_price_sequence()
    test_actual_chart_generation() 