#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模拟UI界面中小数格值的测试
测试用户在界面中设置1.5、2.5、3.5等格值时的实际效果
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

from models.point_figure_model import PointFigureModel

def create_realistic_stock_data():
    """创建更真实的股票数据"""
    # 模拟一只股票从8元到20元的价格走势
    np.random.seed(42)  # 固定随机种子，确保结果可重现
    
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    
    # 创建一个上涨趋势的股票数据
    base_prices = np.linspace(8, 20, 50)
    noise = np.random.normal(0, 0.3, 50)
    
    highs = base_prices + noise + np.abs(np.random.normal(0, 0.2, 50))
    lows = base_prices + noise - np.abs(np.random.normal(0, 0.2, 50))
    opens = base_prices + noise
    closes = base_prices + noise + np.random.normal(0, 0.1, 50)
    volumes = np.random.randint(1000, 5000, 50)
    
    # 确保high >= low，open和close在high和low之间
    for i in range(50):
        if highs[i] < lows[i]:
            highs[i], lows[i] = lows[i], highs[i]
        opens[i] = max(lows[i], min(highs[i], opens[i]))
        closes[i] = max(lows[i], min(highs[i], closes[i]))
    
    return pd.DataFrame({
        'date': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })

def test_ui_scenario():
    """测试UI界面中的实际使用场景"""
    print("=== 模拟UI界面中的小数格值测试 ===")
    
    # 创建真实的股票数据
    stock_data = create_realistic_stock_data()
    print(f"股票数据范围: {stock_data['low'].min():.2f} - {stock_data['high'].max():.2f}")
    
    model = PointFigureModel()
    
    # 测试用户常用的小数格值
    test_box_sizes = [1.5, 2.5, 3.5]
    reversal_amount = 3
    
    for box_size in test_box_sizes:
        print(f"\n{'='*60}")
        print(f"测试格值: {box_size}")
        print(f"{'='*60}")
        
        try:
            # 生成点数图
            result = model.generate_point_figure_chart(
                stock_data, 
                box_size=box_size, 
                reversal_amount=reversal_amount, 
                method="high_low"
            )
            
            if result is not None and not result.empty:
                prices = result.index.tolist()
                
                print(f"✓ 成功生成点数图")
                print(f"  价格数量: {len(prices)}")
                print(f"  价格范围: {min(prices):.1f} - {max(prices):.1f}")
                
                # 验证价格对齐
                invalid_prices = []
                for price in prices:
                    ratio = price / box_size
                    if abs(ratio - round(ratio)) > 1e-10:
                        invalid_prices.append(price)
                
                if not invalid_prices:
                    print(f"  ✓ 所有 {len(prices)} 个价格都是格值 {box_size} 的整数倍")
                else:
                    print(f"  ✗ 发现 {len(invalid_prices)} 个不是格值整数倍的价格")
                    for price in invalid_prices[:3]:
                        print(f"    错误价格: {price:.6f}, 比值: {price/box_size:.6f}")
                
                # 显示纵轴价格示例（模拟用户在界面中看到的）
                print(f"  纵轴价格示例 (格值 {box_size}):")
                
                # 找到有数据的价格范围
                data_prices = []
                for i, price in enumerate(prices):
                    row_has_data = False
                    for col in range(len(result.columns)):
                        if pd.notna(result.iloc[i, col]):
                            row_has_data = True
                            break
                    if row_has_data:
                        data_prices.append(price)
                
                if data_prices:
                    # 显示数据范围内的价格
                    min_data_price = min(data_prices)
                    max_data_price = max(data_prices)
                    
                    # 显示这个范围内的所有价格
                    range_prices = [p for p in prices if min_data_price <= p <= max_data_price]
                    
                    print(f"    数据范围内的价格 ({min_data_price:.1f} - {max_data_price:.1f}):")
                    for price in range_prices:
                        # 检查是否是格值的整数倍
                        ratio = price / box_size
                        is_multiple = abs(ratio - round(ratio)) < 1e-10
                        status = "✓" if is_multiple else "✗"
                        print(f"      {price:6.1f} {status} ({ratio:.1f} × {box_size})")
                
                # 显示点数图内容预览
                print(f"  点数图内容预览:")
                preview_count = 0
                for i, price in enumerate(prices):
                    if preview_count >= 10:  # 只显示前10行有内容的
                        break
                    
                    row_content = []
                    has_content = False
                    for col in range(min(5, len(result.columns))):
                        symbol = result.iloc[i, col]
                        if pd.notna(symbol):
                            row_content.append(symbol)
                            has_content = True
                        else:
                            row_content.append(' ')
                    
                    if has_content:
                        print(f"    {price:6.1f}: {' '.join(row_content)}")
                        preview_count += 1
                        
            else:
                print(f"  ✗ 生成的点数图为空")
                
        except Exception as e:
            print(f"  ✗ 错误: {str(e)}")
            import traceback
            traceback.print_exc()

def test_specific_problematic_case():
    """测试用户报告的具体问题案例"""
    print(f"\n{'='*60}")
    print("测试用户报告的具体问题")
    print(f"{'='*60}")
    
    # 模拟用户提到的情况：格值1.5时出现6、8、9、10、12、14、15、16、18、20、21
    model = PointFigureModel()
    
    # 创建一个可能导致这种情况的数据集
    problem_data = pd.DataFrame({
        'high': [6.2, 8.1, 9.3, 10.5, 12.2, 14.1, 15.8, 16.2, 18.5, 20.3, 21.1],
        'low': [5.8, 7.5, 8.9, 9.8, 11.5, 13.2, 15.1, 15.5, 17.8, 19.5, 20.2],
        'volume': [1000] * 11
    })
    
    box_size = 1.5
    print(f"测试数据范围: {problem_data['low'].min():.1f} - {problem_data['high'].max():.1f}")
    print(f"格值: {box_size}")
    
    try:
        result = model.generate_point_figure_chart(
            problem_data, 
            box_size=box_size, 
            reversal_amount=3, 
            method="high_low"
        )
        
        if result is not None and not result.empty:
            prices = result.index.tolist()
            
            # 找到实际显示的价格（有数据的行）
            displayed_prices = []
            for i, price in enumerate(prices):
                row_has_data = False
                for col in range(len(result.columns)):
                    if pd.notna(result.iloc[i, col]):
                        row_has_data = True
                        break
                if row_has_data:
                    displayed_prices.append(price)
            
            print(f"实际显示的价格: {[f'{p:.1f}' for p in sorted(displayed_prices)]}")
            
            # 验证这些价格是否都是1.5的整数倍
            print(f"价格验证:")
            for price in sorted(displayed_prices):
                ratio = price / box_size
                is_multiple = abs(ratio - round(ratio)) < 1e-10
                status = "✓" if is_multiple else "✗"
                print(f"  {price:6.1f} {status} ({ratio:.3f} × {box_size})")
                
            # 检查是否出现了用户报告的问题价格
            problem_prices = [6, 8, 9, 10, 12, 14, 15, 16, 18, 20, 21]
            found_problems = []
            for prob_price in problem_prices:
                if prob_price in displayed_prices:
                    ratio = prob_price / box_size
                    if abs(ratio - round(ratio)) > 1e-10:
                        found_problems.append(prob_price)
            
            if found_problems:
                print(f"✗ 发现问题价格: {found_problems}")
                print(f"  这些价格不是格值 {box_size} 的整数倍")
            else:
                print(f"✓ 未发现用户报告的问题价格")
                
        else:
            print("生成的点数图为空")
            
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    test_ui_scenario()
    test_specific_problematic_case() 