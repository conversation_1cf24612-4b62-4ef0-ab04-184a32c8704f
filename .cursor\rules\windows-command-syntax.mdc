---
description: Windows命令行和PowerShell语法规范指南
globs: 
alwaysApply: true
---
# Windows命令行和PowerShell语法规范

## 命令行(CMD)语法

### 基本命令结构
- **命令分隔**:
  - `command1 & command2` - 顺序执行多个命令
  - `command1 && command2` - 仅当command1成功时执行command2
  - `command1 || command2` - 仅当command1失败时执行command2

### 重定向和管道
- `command > file.txt` - 输出重定向到文件(覆盖)
- `command >> file.txt` - 追加输出到文件
- `command 2> error.log` - 错误输出重定向
- `command 2>&1` - 错误输出重定向到标准输出
- `command1 | command2` - 管道传递输出

### 变量使用
- 设置变量: `set VARIABLE=value`
- 使用变量: `echo %VARIABLE%`
- 环境变量: `%PATH%`, `%USERPROFILE%`, `%APPDATA%`

### 特殊字符转义
- 使用`^`转义特殊字符: `echo ^&`
- 引号内特殊字符: `echo "特殊字符 & | >"`

### 路径表示
- 使用反斜杠: `C:\Users\<USER>\Documents`
- 当前目录: `.`
- 上级目录: `..`

## PowerShell语法

### 基本命令结构
- **命令分隔**:
  - `command1; command2` - 顺序执行多个命令
  - `command1 && command2` - 仅当command1成功时执行command2

### 重定向和管道
- `command > file.txt` - 输出重定向到文件
- `command >> file.txt` - 追加输出到文件
- `command 2> error.log` - 错误输出重定向
- `command 2>&1` - 错误输出重定向到标准输出
- `command1 | command2` - 管道传递对象(不仅是文本)

### 变量使用
- 设置变量: `$VARIABLE = "value"`
- 使用变量: `Write-Host $VARIABLE`
- 环境变量: `$env:PATH`, `$env:USERPROFILE`, `$env:APPDATA`

### 特殊字符转义
- 使用反引号(`)转义特殊字符: `` Write-Host `& ``
- 单引号内特殊字符不会被解释: `'包含特殊字符的字符串 & | >'`
- 双引号内变量会被解释: `"变量值: $VARIABLE"`

### 路径表示
- 可使用正斜杠或反斜杠: `C:/Users/<USER>/Documents` 或 `C:\Users\<USER>\Documents`
- 当前目录: `.`
- 上级目录: `..`

## 重要区别

1. **分号处理**:
   - PowerShell使用分号(`;`)分隔命令
   - 在Windows Terminal中使用PowerShell执行wt命令时，需要用反引号(`` ` ``)转义分号

2. **引号处理**:
   - CMD对引号处理较为简单
   - PowerShell中单引号和双引号行为不同:
     - 单引号(`'`)内容按字面值处理
     - 双引号(`"`)内容会解析变量和特殊字符

3. **路径表示**:
   - PowerShell支持正斜杠(`/`)和反斜杠(`\`)
   - CMD通常使用反斜杠(`\`)

4. **变量引用**:
   - CMD: `%VARIABLE%`
   - PowerShell: `$VARIABLE`

5. **命令执行**:
   - CMD: 直接执行`.bat`和`.cmd`文件
   - PowerShell: 需要使用`.\script.ps1`执行脚本，且可能需要调整执行策略

## 应用示例

### 激活虚拟环境
- CMD: `venv\Scripts\activate.bat`
- PowerShell: `.\venv\Scripts\Activate.ps1`

### 执行Python脚本
- CMD: `python main.py --config config.json`
- PowerShell: `python main.py --config config.json`

### 设置环境变量
- CMD: `set PYTHONPATH=.`
- PowerShell: `$env:PYTHONPATH = "."`

### 条件执行
- CMD: `python test.py && echo "测试成功" || echo "测试失败"`
- PowerShell: `python test.py; if ($?) { Write-Host "测试成功" } else { Write-Host "测试失败" }`