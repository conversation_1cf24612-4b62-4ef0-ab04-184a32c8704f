#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试表格预扩展设置功能
"""

import sys
import os

# 添加src目录到路径
src_dir = os.path.join(os.path.dirname(__file__), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from utils.settings_manager import SettingsManager


def test_table_expansion_settings():
    """测试表格预扩展设置功能"""
    print("=== 测试表格预扩展设置功能 ===")
    
    # 创建设置管理器
    settings = SettingsManager()
    
    print("\n1. 测试默认设置值:")
    expand_up = settings.get_setting("table_expansion", "expand_up_rows", 10)
    expand_down = settings.get_setting("table_expansion", "expand_down_rows", 10)
    expand_right = settings.get_setting("table_expansion", "expand_right_columns", 10)
    
    print(f"   向上扩展行数: {expand_up}")
    print(f"   向下扩展行数: {expand_down}")
    print(f"   向右扩展列数: {expand_right}")
    
    print("\n2. 测试设置修改:")
    # 修改设置值
    settings.set_setting("table_expansion", "expand_up_rows", 15)
    settings.set_setting("table_expansion", "expand_down_rows", 20)
    settings.set_setting("table_expansion", "expand_right_columns", 12)
    
    # 保存设置
    if settings.save_settings():
        print("   设置保存成功")
    else:
        print("   设置保存失败")
    
    print("\n3. 测试设置读取:")
    # 重新创建设置管理器，验证设置是否持久化
    new_settings = SettingsManager()
    
    new_expand_up = new_settings.get_setting("table_expansion", "expand_up_rows", 10)
    new_expand_down = new_settings.get_setting("table_expansion", "expand_down_rows", 10)
    new_expand_right = new_settings.get_setting("table_expansion", "expand_right_columns", 10)
    
    print(f"   向上扩展行数: {new_expand_up}")
    print(f"   向下扩展行数: {new_expand_down}")
    print(f"   向右扩展列数: {new_expand_right}")
    
    # 验证设置是否正确保存
    if (new_expand_up == 15 and new_expand_down == 20 and new_expand_right == 12):
        print("\n✅ 表格预扩展设置功能测试通过！")
    else:
        print("\n❌ 表格预扩展设置功能测试失败！")
    
    print("\n4. 恢复默认设置:")
    # 恢复默认设置
    settings.set_setting("table_expansion", "expand_up_rows", 10)
    settings.set_setting("table_expansion", "expand_down_rows", 10)
    settings.set_setting("table_expansion", "expand_right_columns", 10)
    settings.save_settings()
    print("   已恢复默认设置")


def test_settings_dialog_integration():
    """测试设置对话框集成"""
    print("\n=== 测试设置对话框集成 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.settings_dialog import SettingsDialog
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        # 检查是否有表格预扩展相关的控件
        has_expand_up = hasattr(dialog, 'expand_up_spin')
        has_expand_down = hasattr(dialog, 'expand_down_spin')
        has_expand_right = hasattr(dialog, 'expand_right_spin')
        
        print(f"   向上扩展控件: {'✅' if has_expand_up else '❌'}")
        print(f"   向下扩展控件: {'✅' if has_expand_down else '❌'}")
        print(f"   向右扩展控件: {'✅' if has_expand_right else '❌'}")
        
        if has_expand_up and has_expand_down and has_expand_right:
            print("\n✅ 设置对话框集成测试通过！")
            
            # 测试控件的默认值
            print(f"\n   控件默认值:")
            print(f"   向上扩展: {dialog.expand_up_spin.value()}")
            print(f"   向下扩展: {dialog.expand_down_spin.value()}")
            print(f"   向右扩展: {dialog.expand_right_spin.value()}")
        else:
            print("\n❌ 设置对话框集成测试失败！")
            
    except ImportError as e:
        print(f"   无法导入PyQt5或设置对话框: {e}")
        print("   跳过UI集成测试")
    except Exception as e:
        print(f"   设置对话框测试出错: {e}")


if __name__ == "__main__":
    test_table_expansion_settings()
    test_settings_dialog_integration()
    
    print("\n=== 测试完成 ===")
