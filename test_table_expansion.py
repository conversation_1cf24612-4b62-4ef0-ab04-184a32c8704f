#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试表格扩展功能
"""

import sys
import os
import pandas as pd

# 添加src目录到路径
src_dir = os.path.join(os.path.dirname(__file__), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_table_expansion():
    """测试表格扩展功能"""
    
    # 创建测试数据
    test_data = pd.DataFrame({
        0: ['X', 'O', 'X', None, None],
        1: [None, 'X', 'O', 'X', None],
        2: [None, None, 'X', 'O', 'X']
    }, index=[10.0, 9.5, 9.0, 8.5, 8.0])
    
    print("原始数据:")
    print(f"价格范围: {test_data.index.min():.1f} - {test_data.index.max():.1f}")
    print(f"列数: {len(test_data.columns)}")
    print(test_data)
    print()
    
    # 模拟目标位计算
    min_target = 7.0  # 低于当前最低价
    max_target = 12.0  # 高于当前最高价
    box_size = 0.5
    
    print(f"目标位: {min_target:.1f} - {max_target:.1f}")
    print(f"格值: {box_size}")
    print()
    
    # 模拟扩展逻辑
    current_prices = test_data.index.tolist()
    current_min_price = min(current_prices)
    current_max_price = max(current_prices)
    
    # 计算需要扩展的价格范围
    target_min_price = min(min_target, max_target)
    target_max_price = max(min_target, max_target)
    
    # 添加缓冲区
    buffer_boxes = 5
    buffer_amount = buffer_boxes * box_size
    
    # 计算新的价格范围
    new_min_price = min(current_min_price, target_min_price - buffer_amount)
    new_max_price = max(current_max_price, target_max_price + buffer_amount)
    
    print(f"扩展前价格范围: {current_min_price:.1f} - {current_max_price:.1f}")
    print(f"扩展后价格范围: {new_min_price:.1f} - {new_max_price:.1f}")
    print()
    
    # 生成新的价格序列
    new_prices = []
    
    # 将价格对齐到格值边界
    aligned_min_price = box_size * round(new_min_price / box_size)
    aligned_max_price = box_size * round(new_max_price / box_size)
    
    # 确保范围足够大
    if aligned_min_price > new_min_price:
        aligned_min_price -= box_size
    if aligned_max_price < new_max_price:
        aligned_max_price += box_size
    
    current_price = aligned_min_price
    while current_price <= aligned_max_price + 1e-10:
        formatted_price = round(current_price, 1)
        new_prices.append(formatted_price)
        current_price += box_size
    
    # 确保价格序列按降序排列
    new_prices.sort(reverse=True)
    
    print(f"新价格序列 ({len(new_prices)} 个价格):")
    print(new_prices[:10], "..." if len(new_prices) > 10 else "")
    print()
    
    # 扩展列数
    current_cols = len(test_data.columns)
    extra_cols = 10
    new_cols = current_cols + extra_cols
    
    print(f"扩展前列数: {current_cols}")
    print(f"扩展后列数: {new_cols}")
    print()
    
    # 创建新的扩展数据框
    extended_data = pd.DataFrame(index=new_prices, columns=range(new_cols))
    
    # 复制原有数据到新数据框中
    for price in current_prices:
        if price in extended_data.index:
            for col in range(current_cols):
                if col in test_data.columns:
                    extended_data.loc[price, col] = test_data.loc[price, col]
    
    print("扩展后的数据:")
    print(f"价格范围: {extended_data.index.min():.1f} - {extended_data.index.max():.1f}")
    print(f"列数: {len(extended_data.columns)}")
    print("前5行，前5列:")
    print(extended_data.iloc[:5, :5])
    print()
    
    # 检查目标位是否在范围内
    target_in_range = (min_target >= extended_data.index.min() and 
                      min_target <= extended_data.index.max() and
                      max_target >= extended_data.index.min() and 
                      max_target <= extended_data.index.max())
    
    print(f"目标位是否在扩展后的范围内: {target_in_range}")
    
    if target_in_range:
        print("✅ 表格扩展功能测试通过！")
    else:
        print("❌ 表格扩展功能测试失败！")

if __name__ == "__main__":
    test_table_expansion()
