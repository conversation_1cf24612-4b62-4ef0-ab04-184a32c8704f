#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图模型
用于生成威科夫点数图数据
"""

import numpy as np
import pandas as pd
import math


class PointFigureModel:
    """点数图模型类"""

    def __init__(self):
        """初始化点数图模型"""
        pass

    def _align_price_to_box_size(self, price, box_size):
        """
        将价格对齐到格值的整数倍，处理浮点精度问题
        
        参数:
            price (float): 原始价格
            box_size (float): 格值大小
            
        返回:
            float: 对齐后的价格（格值的整数倍）
        """
        if box_size <= 0:
            raise ValueError("格值必须大于0")
        
        # 使用round避免浮点精度问题，确保价格是格值的精确整数倍
        aligned_price = round(round(price / box_size) * box_size, 8)
        return aligned_price

    def _handle_reversal_without_enough_symbols(self, chart_data, current_direction, current_price, target_price, box_size, current_date, current_volume):
        """
        处理有反转但符号数量不足的情况

        参数:
            chart_data (list): 点数图数据
            current_direction (str): 当前方向，'X'或'O'
            current_price (float): 当前价格
            target_price (float): 目标价格
            box_size (float): 格子大小
            current_date (str): 当前日期
            current_volume (float): 当前成交量

        返回:
            bool: 是否已处理
        """
        if not chart_data or not chart_data[-1]:
            return False

        # 获取当前列中符号数量 - 修复元组解包问题
        # 兼容处理两元素元组(symbol, price)和四元素元组(symbol, price, date, volume)
        symbols_count = sum(1 for item in chart_data[-1] if isinstance(item, tuple) and len(item) > 0 and item[0] == current_direction)

        # 如果符号数量大于0，继续绘制反向符号
        if symbols_count >= 1:
            # 当前列至少有1个符号时，处理反转情况

            # 对于上涨方向反转为下跌的情况
            if current_direction == 'X':
                # 计算需要向下画多少格
                # 当前价格减去目标价格，除以格子大小，向下取整得到格子数
                boxes_down = math.floor((current_price - target_price) / box_size)

                # 循环画出每一格O
                for j in range(boxes_down):
                    # 每次减去一个格子的价格
                    current_price -= box_size
                    # 在当前列末尾添加一个O符号及其对应价格
                    chart_data[-1].append(('O', current_price, current_date, current_volume))

            # 对于下跌方向反转为上涨的情况
            else:  # current_direction == 'O'
                # 计算需要向上画多少格
                # 目标价格减去当前价格，除以格子大小，向下取整得到格子数
                boxes_up = math.floor((target_price - current_price) / box_size)

                # 循环画出每一格X
                for j in range(boxes_up):
                    # 每次增加一个格子的价格
                    current_price += box_size
                    # 在当前列末尾添加一个X符号及其对应价格
                    chart_data[-1].append(('X', current_price, current_date, current_volume))

            # 返回True表示已成功处理反转
            return True

        # 如果当前列没有任何符号，返回False表示未处理
        return False

    def generate_point_figure_chart(self, data, box_size, reversal_amount, method="high_low", extra_columns=3):
        """
        生成点数图数据

        参数:
            data (pd.DataFrame): 包含时间、开盘价、最高价、最低价、收盘价的DataFrame
            box_size (float): 格子大小
            reversal_amount (int): 反转数量
            method (str): 绘制方法，可选值为"high_low"（高低价法）或"close"（收盘价法）
            extra_columns (int): 在最新列后添加的空白列数量，默认为3

        返回:
            pd.DataFrame: 点数图数据
        """
        if data is None or len(data) == 0:
            return None

        # 根据绘制方法选择不同的处理函数
        chart_data = None
        if method == "high_low":
            chart_data = self._generate_high_low_chart(data, box_size, reversal_amount)
        elif method == "close":
            chart_data = self._generate_close_chart(data, box_size, reversal_amount)
        else:
            raise ValueError(f"不支持的绘制方法: {method}")

        # 如果需要添加额外列，向DataFrame的attrs中添加额外列数量信息
        if chart_data is not None and extra_columns > 0:
            chart_data.attrs['extra_columns'] = extra_columns

        return chart_data

    def _generate_high_low_chart(self, data, box_size, reversal_amount):
        """
        使用高低价法生成点数图

        参数:
            data (pd.DataFrame): 包含时间、开盘价、最高价、最低价、收盘价的DataFrame
            box_size (float): 格子大小
            reversal_amount (int): 反转数量

        返回:
            pd.DataFrame: 点数图数据
        """
        # 确保数据包含必要的列
        required_columns = ['high', 'low']
        if not all(col in data.columns for col in required_columns):
            raise ValueError("数据必须包含'high'和'low'列")

        # 检查数据中的时间列并处理日期和时间
        # 对于分钟级数据，date和time列合并；对于日线数据，使用date或索引
        date_column = None
        time_column = None

        # 确定日期列
        if 'date' in data.columns:
            date_column = 'date'
        elif 'datetime' in data.columns:
            date_column = 'datetime'
        elif data.index.name:
            date_column = data.index.name

        # 确定时间列(分钟级数据)
        if 'time' in data.columns:
            time_column = 'time'

        # 准备日期信息
        if date_column and date_column in data.columns:
            dates = data[date_column].values
        else:
            # 使用索引作为日期
            dates = data.index.values

        # 如果有时间列，组合日期和时间
        if time_column and time_column in data.columns:
            # 组合日期和时间为完整字符串 (例如: '2023-05-01 10:30:00')
            date_time_strings = []
            for i in range(len(dates)):
                date_str = str(dates[i])
                if isinstance(dates[i], pd.Timestamp):
                    date_str = dates[i].strftime('%Y-%m-%d')

                time_str = str(data[time_column].iloc[i])
                # 确保时间格式为HHMMSS
                if len(time_str) == 6 and time_str.isdigit():
                    formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                    date_time_strings.append(f"{date_str} {formatted_time}")
                else:
                    date_time_strings.append(date_str)

            dates = date_time_strings

        # 获取最高价和最低价
        high_prices = data['high'].values
        low_prices = data['low'].values

        # 获取成交量
        volumes = data['volume'].values if 'volume' in data.columns else np.zeros(len(high_prices))

        # 初始化点数图数据
        chart_data = []

        # 确保至少有两行数据
        if len(high_prices) < 2 or len(low_prices) < 2:
            return self._convert_to_dataframe([], box_size=box_size)

        # 获取前两行的高低价
        H1, L1 = high_prices[0], low_prices[0]
        H2, L2 = high_prices[1], low_prices[1]

        # 获取前两行的日期和成交量
        date1, date2 = dates[0], dates[1] if len(dates) > 1 else dates[0]
        volume1, volume2 = volumes[0], volumes[1] if len(volumes) > 1 else volumes[0]

        # 计算初始列的总成交量
        initial_volume = volume1 + volume2

        # 判断初始方向（根据流程图规则）
        # 如果H1-L2 > H2-L1，则初始方向为下跌(O)，否则为上涨(X)
        if H1 - L2 > H2 - L1:
            current_direction = 'O'
            # 起始格 = 向下取整(L1 / 格值) × 格值
            current_price = math.floor(L1 / box_size) * box_size

            # 计算下跌的格数 - 保证精确计算到L2
            boxes_down = max(0, math.floor((current_price - L2) / box_size))
            total_boxes = boxes_down + 1  # 加上起始格

            # 计算每个格子分配的成交量
            volume_per_box = initial_volume / total_boxes if total_boxes > 0 else 0

            # 从起始格向下开始画O至L2对应格
            current_column = [('O', current_price, date1, volume_per_box)]

            # 添加O符号
            for j in range(boxes_down):
                current_price -= box_size
                current_column.append(('O', current_price, date2, volume_per_box))
        else:
            current_direction = 'X'
            # 起始格 = 向上取整(H1 / 格值) × 格值
            current_price = math.ceil(H1 / box_size) * box_size

            # 计算上涨的格数 - 保证精确计算到H2
            boxes_up = max(0, math.floor((H2 - current_price) / box_size))
            total_boxes = boxes_up + 1  # 加上起始格

            # 计算每个格子分配的成交量
            volume_per_box = initial_volume / total_boxes if total_boxes > 0 else 0

            # 从起始格向上开始画X至H2对应格
            current_column = [('X', current_price, date1, volume_per_box)]

            # 添加X符号
            for j in range(boxes_up):
                current_price += box_size
                current_column.append(('X', current_price, date2, volume_per_box))

        # 如果初始列有符号，添加到图表数据
        if current_column:
            chart_data.append(current_column)

        # 处理后续数据（从第三行开始）
        for i in range(2, len(data)):
            high = high_prices[i]
            low = low_prices[i]
            current_date = dates[i]
            current_volume = volumes[i]

            # 获取当前列的有效方向
            if chart_data and chart_data[-1]:
                # 通过检查当前列中最后一个X或O符号来确定当前有效方向
                # 首先按照添加顺序收集所有符号
                symbols_in_column = [item[0] for item in chart_data[-1]]

                # 从后向前找最后一个X和最后一个O
                last_x_index = symbols_in_column[::-1].index('X') if 'X' in symbols_in_column else -1
                last_o_index = symbols_in_column[::-1].index('O') if 'O' in symbols_in_column else -1

                # 如果找到了符号，将索引转换为倒序索引
                if last_x_index != -1:
                    last_x_index = len(symbols_in_column) - 1 - last_x_index
                if last_o_index != -1:
                    last_o_index = len(symbols_in_column) - 1 - last_o_index

                # 确定当前有效方向（以最后出现的符号为准）
                if last_x_index != -1 and last_o_index != -1:
                    # 如果X和O都存在，比较它们的位置，取最后出现的
                    current_direction = 'X' if last_x_index > last_o_index else 'O'
                elif last_x_index != -1:
                    current_direction = 'X'
                elif last_o_index != -1:
                    current_direction = 'O'
                else:
                    # 如果当前列没有有效符号，使用最后一个符号的方向
                    last_symbol = chart_data[-1][-1][0]
                    current_direction = last_symbol
            else:
                # 如果没有当前列，继续下一行
                continue

            # 创建新列
            new_column = []

            if current_direction == 'X':
                # 获取当前列的最高X格值
                highest_x_price = max([item[1] for item in chart_data[-1] if item[0] == 'X'], default=0)

                # 获取最后一个X的价格
                last_x_prices = [item[1] for item in chart_data[-1] if item[0] == 'X']
                last_x_price = last_x_prices[-1] if last_x_prices else 0

                # 判断新高：Hn >= 当前列最高X格值 + 格值？（修改为>=，确保边界情况正确处理）
                if high >= highest_x_price + box_size:
                    # 计算上涨的格数
                    boxes_up = math.floor((high - highest_x_price) / box_size)

                    # 计算每个格子分配的成交量
                    volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                    # 在当前列继续向上画X
                    current_price = highest_x_price
                    for j in range(boxes_up):
                        current_price += box_size
                        chart_data[-1].append(('X', current_price, current_date, volume_per_box))

                # 判断反转：Ln <= 当前列最高X格值 - k格？
                elif low <= highest_x_price - reversal_amount * box_size:
                    # 检查符号数量是否≥k
                    x_symbols_count = sum(1 for item in chart_data[-1] if item[0] == 'X')

                    # 符号数量要求：一点图至少2个符号，其他情况至少k个符号
                    min_symbols = 2 if reversal_amount == 1 else reversal_amount

                    # 增加一种情况判断：如果当前列有其他符号，但X数量不足，则可能需要调整最小符号数要求
                    has_other_symbols = any(item[0] == 'O' for item in chart_data[-1])

                    if x_symbols_count >= min_symbols or (has_other_symbols and x_symbols_count >= 1):
                        # 换列绘制O：起始位置 = 当前列最高X格值 - 1格
                        current_price = highest_x_price - box_size

                        # 从起始位置开始画O至Ln对应格
                        boxes_down = math.floor((current_price - low) / box_size)

                        # 计算每个格子分配的成交量
                        volume_per_box = current_volume / (boxes_down + 1) if boxes_down >= 0 else current_volume

                        # 添加起始位置的符号
                        new_column.append(('O', current_price, current_date, volume_per_box))

                        # 画剩余的符号 (如果有的话)
                        for j in range(boxes_down):
                            current_price -= box_size
                            new_column.append(('O', current_price, current_date, volume_per_box))

                        # 添加新列 - 确保即使只有一个符号也会添加
                        chart_data.append(new_column)
                    elif reversal_amount == 1 and x_symbols_count >= 1:
                        # 一点图特殊处理：允许同一列X/O共存
                        current_price = highest_x_price
                        boxes_down = math.floor((current_price - low) / box_size)

                        # 计算每个格子分配的成交量
                        volume_per_box = current_volume / boxes_down if boxes_down > 0 else current_volume

                        for j in range(boxes_down):
                            current_price -= box_size
                            chart_data[-1].append(('O', current_price, current_date, volume_per_box))
                    else:
                        # 规则要求：符号数量不足但满足反转条件时，继续当前列画反向符号
                        # 非一点图时，使用辅助函数处理符号数量≥1但<k的情况
                        self._handle_reversal_without_enough_symbols(chart_data, 'X', highest_x_price, low, box_size, current_date, current_volume)

            else:  # current_direction == 'O'
                # 获取当前列的最低O格值
                lowest_o_price = min([item[1] for item in chart_data[-1] if item[0] == 'O'], default=float('inf'))

                # 获取最后一个O的价格
                last_o_prices = [item[1] for item in chart_data[-1] if item[0] == 'O']
                last_o_price = last_o_prices[-1] if last_o_prices else float('inf')

                # 判断新低：Ln <= 当前列最低O格值 - 格值？
                if low <= lowest_o_price - box_size:
                    # 计算下跌的格数
                    boxes_down = math.floor((lowest_o_price - low) / box_size)

                    # 计算每个格子分配的成交量
                    volume_per_box = current_volume / boxes_down if boxes_down > 0 else current_volume

                    # 在当前列继续向下画O
                    current_price = lowest_o_price
                    for j in range(boxes_down):
                        current_price -= box_size
                        chart_data[-1].append(('O', current_price, current_date, volume_per_box))

                # 判断反转：Hn >= 当前列最低O格值 + k格？
                elif high >= lowest_o_price + reversal_amount * box_size:
                    # 检查符号数量是否≥k
                    o_symbols_count = sum(1 for item in chart_data[-1] if item[0] == 'O')

                    # 符号数量要求：一点图至少2个符号，其他情况至少k个符号
                    min_symbols = 2 if reversal_amount == 1 else reversal_amount

                    # 增加一种情况判断：如果当前列有其他符号，但O数量不足，则可能需要调整最小符号数要求
                    has_other_symbols = any(item[0] == 'X' for item in chart_data[-1])

                    if o_symbols_count >= min_symbols or (has_other_symbols and o_symbols_count >= 1):
                        # 换列绘制X：起始位置 = 当前列最低O格值 + 1格
                        current_price = lowest_o_price + box_size

                        # 从起始位置开始画X至Hn对应格
                        boxes_up = math.floor((high - current_price) / box_size)

                        # 计算每个格子分配的成交量
                        volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                        # 添加起始位置的符号
                        new_column.append(('X', current_price, current_date, volume_per_box))

                        # 画剩余的符号 (如果有的话)
                        for j in range(boxes_up):
                            current_price += box_size
                            new_column.append(('X', current_price, current_date, volume_per_box))

                        # 添加新列 - 确保即使只有一个符号也会添加
                        chart_data.append(new_column)
                    elif reversal_amount == 1 and o_symbols_count >= 1:
                        # 一点图特殊处理：允许同一列O/X共存
                        current_price = lowest_o_price
                        boxes_up = math.floor((high - current_price) / box_size)

                        # 计算每个格子分配的成交量
                        volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                        for j in range(boxes_up):
                            current_price += box_size
                            chart_data[-1].append(('X', current_price, current_date, volume_per_box))
                    else:
                        # 规则要求：符号数量不足但满足反转条件时，继续当前列画反向符号
                        # 非一点图时，使用辅助函数处理符号数量≥1但<k的情况
                        self._handle_reversal_without_enough_symbols(chart_data, 'O', lowest_o_price, high, box_size, current_date, current_volume)

        # 转换为DataFrame，使用设置中的额外列参数
        return self._convert_to_dataframe(chart_data, box_size=box_size)

    def _generate_close_chart(self, data, box_size, reversal_amount):
        """
        使用收盘价法生成点数图

        参数:
            data (pd.DataFrame): 包含时间、开盘价、最高价、最低价、收盘价的DataFrame
            box_size (float): 格子大小
            reversal_amount (int): 反转数量

        返回:
            pd.DataFrame: 点数图数据
        """
        # 确保数据包含必要的列
        required_columns = ['close']
        if not all(col in data.columns for col in required_columns):
            raise ValueError("数据必须包含'close'列")

        # 检查数据中的时间列并处理日期和时间
        # 对于分钟级数据，date和time列合并；对于日线数据，使用date或索引
        date_column = None
        time_column = None

        # 确定日期列
        if 'date' in data.columns:
            date_column = 'date'
        elif 'datetime' in data.columns:
            date_column = 'datetime'
        elif data.index.name:
            date_column = data.index.name

        # 确定时间列(分钟级数据)
        if 'time' in data.columns:
            time_column = 'time'

        # 准备日期信息
        if date_column and date_column in data.columns:
            dates = data[date_column].values
        else:
            # 使用索引作为日期
            dates = data.index.values

        # 如果有时间列，组合日期和时间
        if time_column and time_column in data.columns:
            # 组合日期和时间为完整字符串 (例如: '2023-05-01 10:30:00')
            date_time_strings = []
            for i in range(len(dates)):
                date_str = str(dates[i])
                if isinstance(dates[i], pd.Timestamp):
                    date_str = dates[i].strftime('%Y-%m-%d')

                time_str = str(data[time_column].iloc[i])
                # 确保时间格式为HHMMSS
                if len(time_str) == 6 and time_str.isdigit():
                    formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                    date_time_strings.append(f"{date_str} {formatted_time}")
                else:
                    date_time_strings.append(date_str)

            dates = date_time_strings

        # 获取收盘价
        close_prices = data['close'].values

        # 获取成交量
        volumes = data['volume'].values if 'volume' in data.columns else np.zeros(len(close_prices))

        # 初始化点数图数据
        chart_data = []

        # 确保至少有一行数据
        if len(close_prices) < 1:
            return self._convert_to_dataframe([], box_size=box_size)

        # 获取第一个收盘价、日期和成交量
        first_close = close_prices[0]
        first_date = dates[0]
        first_volume = volumes[0]

        # 初始化第一列
        first_column = []

        # 确定初始价格的格子位置和方向（收盘价方法通常从第一天的收盘价开始）
        initial_price = math.floor(first_close / box_size) * box_size

        # 确定下一个交易日的方向
        if len(close_prices) > 1:
            # 如果第二天收盘价高于第一天，方向为上涨，否则为下跌
            is_up = close_prices[1] >= first_close

            # 收集初始成交量（使用前两天的总成交量）
            initial_volume = volumes[0]
            if len(volumes) > 1:
                initial_volume += volumes[1]

            # 初始日期
            initial_date = dates[0]

            if is_up:
                # 上涨：从初始价格格子开始，画X至第一天收盘价的格子
                current_price = initial_price
                while current_price <= first_close:
                    # 分配成交量到每个格子
                    first_column.append(('X', current_price, initial_date, initial_volume / 2))  # 平均分配
                    current_price += box_size

                # 已经添加了第一天的收盘价格子，现在处理第二天
                boxes_up = math.floor((close_prices[1] - first_close) / box_size)
                # 计算每个格子分配的成交量
                volume_per_box = volumes[1] / (boxes_up + 1) if boxes_up >= 0 else volumes[1]
                current_price = first_close  # 从第一天的收盘价开始

                # 处理第二天的价格变动
                for j in range(boxes_up):
                    current_price += box_size
                    first_column.append(('X', current_price, dates[1], volume_per_box))
            else:
                # 下跌：从初始价格格子开始，画O至第一天收盘价的格子
                current_price = initial_price
                while current_price >= first_close:
                    first_column.append(('O', current_price, initial_date, initial_volume / 2))  # 平均分配
                    current_price -= box_size

                # 已经添加了第一天的收盘价格子，现在处理第二天
                boxes_down = math.floor((first_close - close_prices[1]) / box_size)
                # 计算每个格子分配的成交量
                volume_per_box = volumes[1] / (boxes_down + 1) if boxes_down >= 0 else volumes[1]
                current_price = first_close  # 从第一天的收盘价开始

                # 处理第二天的价格变动
                for j in range(boxes_down):
                    current_price -= box_size
                    first_column.append(('O', current_price, dates[1], volume_per_box))

            # 添加第一列
            chart_data.append(first_column)

        # 处理后续数据（从第三行开始）
        for i in range(2, len(data)):
            close = close_prices[i]
            current_date = dates[i]
            current_volume = volumes[i]

            # 获取当前列的有效方向
            if chart_data and chart_data[-1]:
                # 通过检查当前列中最后一个X或O符号来确定当前有效方向
                # 首先按照添加顺序收集所有符号
                symbols_in_column = [item[0] for item in chart_data[-1]]

                # 从后向前找最后一个X和最后一个O
                last_x_index = symbols_in_column[::-1].index('X') if 'X' in symbols_in_column else -1
                last_o_index = symbols_in_column[::-1].index('O') if 'O' in symbols_in_column else -1

                # 如果找到了符号，将索引转换为倒序索引
                if last_x_index != -1:
                    last_x_index = len(symbols_in_column) - 1 - last_x_index
                if last_o_index != -1:
                    last_o_index = len(symbols_in_column) - 1 - last_o_index

                # 确定当前有效方向（以最后出现的符号为准）
                if last_x_index != -1 and last_o_index != -1:
                    # 如果X和O都存在，比较它们的位置，取最后出现的
                    current_direction = 'X' if last_x_index > last_o_index else 'O'
                elif last_x_index != -1:
                    current_direction = 'X'
                elif last_o_index != -1:
                    current_direction = 'O'
                else:
                    # 如果当前列没有有效符号，使用最后一个符号的方向
                    last_symbol = chart_data[-1][-1][0]
                    current_direction = last_symbol
            else:
                # 如果没有当前列，继续下一行
                continue

            # 创建处理后续收盘价的逻辑
            new_column = []
            close_box = math.floor(close / box_size) * box_size  # 四舍五入到最接近的格子价格

            if current_direction == 'X':
                # 获取当前列的最高X格值
                highest_x_price = max([item[1] for item in chart_data[-1] if item[0] == 'X'], default=0)

                # 判断继续上涨：close_box > 当前列最高X格值？
                if close_box > highest_x_price:
                    # 计算上涨的格数
                    boxes_up = math.floor((close_box - highest_x_price) / box_size)

                    # 计算每个格子分配的成交量
                    volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                    # 在当前列继续向上画X
                    current_price = highest_x_price
                    for j in range(boxes_up):
                        current_price += box_size
                        chart_data[-1].append(('X', current_price, current_date, volume_per_box))

                # 判断反转：close_box <= 当前列最高X格值 - k格？
                elif close_box <= highest_x_price - reversal_amount * box_size:
                    # 检查符号数量是否≥k
                    x_symbols_count = sum(1 for item in chart_data[-1] if item[0] == 'X')

                    # 符号数量要求：一点图至少2个符号，其他情况至少k个符号
                    min_symbols = 2 if reversal_amount == 1 else reversal_amount

                    # 增加一种情况判断：如果当前列有其他符号，但X数量不足，则可能需要调整最小符号数要求
                    has_other_symbols = any(item[0] == 'O' for item in chart_data[-1])

                    if x_symbols_count >= min_symbols or (has_other_symbols and x_symbols_count >= 1):
                        # 换列绘制O：起始位置 = 当前列最高X格值 - 1格
                        current_price = highest_x_price - box_size

                        # 计算下降格数
                        boxes_down = math.floor((current_price - close_box) / box_size)

                        # 计算每个格子分配的成交量
                        volume_per_box = current_volume / (boxes_down + 1) if boxes_down >= 0 else current_volume

                        # 添加起始位置的符号
                        new_column.append(('O', current_price, current_date, volume_per_box))

                        # 画剩余的符号 (如果有的话)
                        for j in range(boxes_down):
                            current_price -= box_size
                            new_column.append(('O', current_price, current_date, volume_per_box))

                        # 添加新列 - 确保即使只有一个符号也会添加
                        chart_data.append(new_column)
                    elif reversal_amount == 1 and x_symbols_count >= 1:
                        # 一点图特殊处理：允许同一列X/O共存
                        current_price = highest_x_price
                        boxes_down = math.floor((current_price - close_box) / box_size)

                        for j in range(boxes_down):
                            current_price -= box_size
                            chart_data[-1].append(('O', current_price, current_date, current_volume))
                    else:
                        # 规则要求：符号数量不足但满足反转条件时，继续当前列画反向符号
                        # 非一点图时，使用辅助函数处理符号数量≥1但<k的情况
                        self._handle_reversal_without_enough_symbols(chart_data, 'X', highest_x_price, close_box, box_size, current_date, current_volume)

            else:  # current_direction == 'O'
                # 获取当前列的最低O格值
                lowest_o_price = min([item[1] for item in chart_data[-1] if item[0] == 'O'], default=float('inf'))

                # 获取最后一个O的价格
                last_o_prices = [item[1] for item in chart_data[-1] if item[0] == 'O']
                last_o_price = last_o_prices[-1] if last_o_prices else float('inf')

                # 判断新低：Cn <= 当前列最低O格值 - 格值？
                if close <= lowest_o_price - box_size:
                    # 计算下跌的格数
                    boxes_down = math.floor((lowest_o_price - close) / box_size)

                    # 在当前列继续向下画O
                    current_price = lowest_o_price
                    for j in range(boxes_down):
                        current_price -= box_size
                        chart_data[-1].append(('O', current_price, current_date, current_volume))

                # 判断反转（上涨）
                else:
                    # 计算收盘价对应的格值
                    close_box = math.ceil(close / box_size) * box_size

                    # 反向移动距离 = 收盘价对应的格值 - 当前列最低O格值
                    reverse_distance = math.floor((close_box - lowest_o_price) / box_size)

                    # 若反向移动距离 ≥k格
                    if reverse_distance >= reversal_amount:
                        # 检查符号数量是否≥k
                        o_symbols_count = sum(1 for item in chart_data[-1] if item[0] == 'O')

                        # 符号数量要求：一点图至少2个符号，其他情况至少k个符号
                        min_symbols = 2 if reversal_amount == 1 else reversal_amount

                        # 增加一种情况判断：如果当前列有其他符号，但O数量不足，则可能需要调整最小符号数要求
                        has_other_symbols = any(item[0] == 'X' for item in chart_data[-1])

                        if o_symbols_count >= min_symbols or (has_other_symbols and o_symbols_count >= 1):
                            # 换列绘制X：起始位置 = 当前列最低O格值 + 1格
                            current_price = lowest_o_price + box_size

                            # 从起始位置开始画X至收盘价对应格
                            boxes_up = math.floor((close_box - current_price) / box_size)

                            # 计算每个格子分配的成交量
                            volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                            # 添加起始位置的符号
                            new_column.append(('X', current_price, current_date, volume_per_box))

                            # 画剩余的符号 (如果有的话)
                            for j in range(boxes_up):
                                current_price += box_size
                                new_column.append(('X', current_price, current_date, volume_per_box))

                            # 添加新列 - 确保即使只有一个符号也会添加
                            chart_data.append(new_column)
                        elif reversal_amount == 1 and o_symbols_count >= 1:
                            # 一点图特殊处理：允许同一列O/X共存
                            current_price = lowest_o_price
                            boxes_up = math.floor((high - current_price) / box_size)

                            # 计算每个格子分配的成交量
                            volume_per_box = current_volume / boxes_up if boxes_up > 0 else current_volume

                            for j in range(boxes_up):
                                current_price += box_size
                                chart_data[-1].append(('X', current_price, current_date, volume_per_box))
                        else:
                            # 规则要求：符号数量不足但满足反转条件时，继续当前列画反向符号
                            # 非一点图时，使用辅助函数处理符号数量≥1但<k的情况
                            self._handle_reversal_without_enough_symbols(chart_data, 'O', lowest_o_price, close_box, box_size, current_date, current_volume)

        # 转换为DataFrame，使用设置中的额外列参数
        return self._convert_to_dataframe(chart_data, box_size=box_size)

    def _convert_to_dataframe(self, chart_data, extra_columns=None, box_size=None):
        """
        将点数图数据转换为DataFrame

        参数:
            chart_data (list): 点数图数据，每个元素是一列，每列是一个元组列表
                              元组包含(符号, 价格, 日期, 成交量)
            extra_columns (int): 在最新列后添加的空白列数量，如果为None则从设置中读取
            box_size (float): 格值，用于计算价格扩展

        返回:
            pd.DataFrame: 点数图数据
        """
        # 如果没有指定extra_columns，从设置中读取
        if extra_columns is None:
            from utils.settings_manager import SettingsManager
            settings = SettingsManager()
            extra_columns = settings.get_setting("table_expansion", "expand_right_columns", 10)
        if not chart_data:
            return pd.DataFrame()

        # 获取所有价格并确保是数值类型，同时进行格值化处理
        all_prices = set()
        for column in chart_data:
            for item in column:
                # 兼容旧版本数据格式(symbol, price)和新格式(symbol, price, date, volume)
                if len(item) >= 2:
                    _, price = item[0], item[1]
                    # 确保价格是数值类型
                    try:
                        # 统一转换为float类型
                        if isinstance(price, (int, float)):
                            price = float(price)
                        elif isinstance(price, str):
                            price = float(price)
                        else:
                            print(f"警告：未知的价格类型 '{type(price)}'，值为 '{price}'，已跳过")
                            continue
                        
                        # 确保box_size有效
                        if box_size is None or box_size <= 0:
                            print(f"警告：无效的格值 '{box_size}'，使用默认值0.5")
                            box_size = 0.5
                        
                        # 将价格对齐到格值的整数倍
                        aligned_price = self._align_price_to_box_size(price, box_size)
                        all_prices.add(aligned_price)
                    except (ValueError, TypeError) as e:
                        print(f"警告：无法将价格 '{price}' 转换为数值类型: {str(e)}，已跳过")
                        continue

        # 应用向上向下的预扩展
        if all_prices:
            from utils.settings_manager import SettingsManager
            settings = SettingsManager()
            expand_up_rows = settings.get_setting("table_expansion", "expand_up_rows", 10)
            expand_down_rows = settings.get_setting("table_expansion", "expand_down_rows", 10)

            # 如果没有传入box_size，使用默认值0.5
            if box_size is None:
                box_size = 0.5  # 默认格值

            current_min = min(all_prices)
            current_max = max(all_prices)

            # 计算当前价格对应的步数
            current_min_step = int(round(current_min / box_size))
            current_max_step = int(round(current_max / box_size))

            # 计算扩展后的步数，添加下限保护和智能扩展
            # 确保最小价格不会小于0，同时避免过度扩展
            
            # 对于较小的价格，限制向下扩展的幅度
            if current_min < box_size * 5:  # 如果最小价格小于5个格值
                actual_expand_down = min(expand_down_rows, current_min_step)
            else:
                actual_expand_down = expand_down_rows
            
            min_step = max(0, current_min_step - actual_expand_down)
            max_step = current_max_step + expand_up_rows
            
            # 计算扩展后的实际价格边界
            extended_min = self._align_price_to_box_size(min_step * box_size, box_size)
            extended_max = self._align_price_to_box_size(max_step * box_size, box_size)

            # 生成扩展的价格序列，确保每个价格都是格值的整数倍
            # 使用整数计数器避免浮点精度累积误差
            for step in range(min_step, max_step + 1):
                price = self._align_price_to_box_size(step * box_size, box_size)
                all_prices.add(price)

            print(f"价格范围预扩展: {current_min:.2f}-{current_max:.2f} -> {extended_min:.2f}-{extended_max:.2f}")
            print(f"扩展参数: 向上{expand_up_rows}行, 向下{actual_expand_down}行(实际), 格值: {box_size}")
            print(f"步数范围: {min_step} 到 {max_step} (原始: {current_min_step} 到 {current_max_step})")
            print(f"总共 {max_step - min_step + 1} 个价格点")

        # 从高到低排序价格
        sorted_prices = sorted(all_prices, reverse=True)
        
        # 验证所有价格都是格值的整数倍
        if box_size and box_size > 0:
            invalid_prices = []
            for price in sorted_prices:
                ratio = price / box_size
                if abs(ratio - round(ratio)) > 1e-10:
                    invalid_prices.append(price)
            
            if invalid_prices:
                print(f"警告：发现 {len(invalid_prices)} 个不是格值整数倍的价格:")
                for price in invalid_prices[:5]:  # 只显示前5个
                    print(f"  价格: {price}, 格值: {box_size}, 比值: {price/box_size}")
                if len(invalid_prices) > 5:
                    print(f"  ... 还有 {len(invalid_prices) - 5} 个")
            else:
                print(f"✓ 所有 {len(sorted_prices)} 个价格都是格值 {box_size} 的整数倍")

        # 计算总列数（原有列 + 额外空白列）
        total_columns = len(chart_data) + extra_columns

        # 预先创建完整数据字典（包括额外空白列）
        symbol_dict = {col_idx: {price: None for price in sorted_prices} for col_idx in range(total_columns)}
        date_dict = {col_idx: {price: None for price in sorted_prices} for col_idx in range(total_columns)}
        volume_dict = {col_idx: {price: None for price in sorted_prices} for col_idx in range(total_columns)}

        # 填充数据字典（仅填充原有列数据，保持额外列为空）
        for col_idx, column in enumerate(chart_data):
            for item in column:
                try:
                    symbol, price = item[0], item[1]
                    date = item[2] if len(item) > 2 else None
                    volume = item[3] if len(item) > 3 else None

                    if isinstance(price, str):
                        price = float(price)

                    symbol_dict[col_idx][price] = symbol
                    if date is not None:
                        date_dict[col_idx][price] = date
                    if volume is not None:
                        volume_dict[col_idx][price] = volume
                except (ValueError, TypeError, KeyError) as e:
                    print(f"警告：处理价格 '{price}' 时出错: {str(e)}")
                    continue

        # 一次性创建DataFrame
        df_symbol = pd.DataFrame(symbol_dict, index=sorted_prices)
        df_date = pd.DataFrame(date_dict, index=sorted_prices)
        df_volume = pd.DataFrame(volume_dict, index=sorted_prices)

        # 将额外信息添加到原始DataFrame的属性中
        df_symbol.attrs['date_info'] = df_date
        df_symbol.attrs['volume_info'] = df_volume

        return df_symbol