#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试价格格值化功能
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

from models.point_figure_model import PointFigureModel
import pandas as pd
import numpy as np

def test_price_alignment():
    """测试价格格值化函数"""
    model = PointFigureModel()
    
    print('=== 测试价格格值化函数 ===')
    test_cases = [
        (11.72, 0.5),   # 应该对齐到12.0
        (11.25, 0.5),   # 应该对齐到11.5
        (23.7, 2.0),    # 应该对齐到24.0
        (23.1, 2.0),    # 应该对齐到24.0
        (12.34, 0.1),   # 应该对齐到12.3
        (12.36, 0.1),   # 应该对齐到12.4
    ]
    
    for price, box_size in test_cases:
        aligned = model._align_price_to_box_size(price, box_size)
        print(f'原价格: {price}, 格值: {box_size}, 对齐后: {aligned}')
        # 验证是否为格值的整数倍
        ratio = aligned / box_size
        is_integer = abs(ratio - round(ratio)) < 1e-10
        print(f'  验证: {aligned} / {box_size} = {ratio:.10f} (是整数倍: {is_integer})')
        print()

def test_price_sequence_generation():
    """测试价格序列生成"""
    model = PointFigureModel()
    
    print('=== 测试价格序列生成 ===')
    
    # 创建简单的测试数据
    test_data = pd.DataFrame({
        'high': [12.5, 13.0, 12.8],
        'low': [12.0, 12.5, 12.3],
        'volume': [1000, 1500, 1200]
    })
    
    # 测试不同格值
    box_sizes = [0.5, 1.0, 0.1]
    
    for box_size in box_sizes:
        print(f'\n--- 格值: {box_size} ---')
        chart_data = model.generate_point_figure_chart(test_data, box_size, 3, "high_low")
        
        if chart_data is not None:
            prices = chart_data.index.tolist()
            print(f'价格数量: {len(prices)}')
            print(f'价格范围: {min(prices):.3f} - {max(prices):.3f}')
            print(f'前5个价格: {[f"{p:.3f}" for p in prices[:5]]}')
            print(f'后5个价格: {[f"{p:.3f}" for p in prices[-5:]]}')
            
            # 验证所有价格都是格值的整数倍
            all_aligned = True
            for price in prices:
                ratio = price / box_size
                if abs(ratio - round(ratio)) > 1e-10:
                    all_aligned = False
                    print(f'  错误: 价格 {price} 不是格值 {box_size} 的整数倍')
                    break
            
            if all_aligned:
                print(f'  ✓ 所有价格都是格值 {box_size} 的整数倍')
            
            # 检查价格序列的连续性
            sorted_prices = sorted(prices, reverse=True)
            gaps = []
            for i in range(len(sorted_prices) - 1):
                gap = sorted_prices[i] - sorted_prices[i + 1]
                if abs(gap - box_size) > 1e-10:
                    gaps.append((sorted_prices[i], sorted_prices[i + 1], gap))
            
            if not gaps:
                print(f'  ✓ 价格序列连续，间隔均为格值 {box_size}')
            else:
                print(f'  ⚠ 发现 {len(gaps)} 个不连续的价格间隔')
                for p1, p2, gap in gaps[:3]:  # 只显示前3个
                    print(f'    {p1:.3f} -> {p2:.3f}, 间隔: {gap:.3f}')

if __name__ == "__main__":
    test_price_alignment()
    test_price_sequence_generation() 