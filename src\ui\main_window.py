#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
威科夫点数图软件主窗口
"""

import os
from PyQt5.QtWidgets import (QMainWindow, QFileDialog, QTextEdit, QTabWidget, QWidget, QVBoxLayout,
                              QHBoxLayout, QGridLayout, QFormLayout, QMenuBar, QMenu, QAction, QMessageBox,
                              QToolBar, QLabel, QStatusBar, QDockWidget, QDialog, QPushButton, QTextEdit)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt, QSize, QEvent

from ui.data_import_dialog import DataImportDialog
from ui.point_figure_chart import PointFigureChartWidget
from ui.settings_dialog import SettingsDialog
from ui.about_dialog import AboutDialog
from ui.data_viewer_dialog import DataViewerDialog

from controllers.data_controller import DataController
from controllers.chart_controller import ChartController
from modules.point_figure_module.point_figure_module import PointFigureModule
from utils.settings_manager import SettingsManager

# 导入版本号
import src
__version__ = src.__version__


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 初始化设置管理器
        self.settings = SettingsManager()

        # 初始化控制器
        self.data_controller = DataController()
        self.chart_controller = ChartController()

        # 将数据控制器传递给图表控制器
        self.chart_controller.data_controller = self.data_controller

        # 初始化点数图模块
        self.point_figure_module = PointFigureModule()
        self.point_figure_module.initialize()

        # 初始化界面
        self._init_ui()

        # 连接信号
        self._connect_signals()

        # 启动时自动最大化窗口
        self.showMaximized()

        # 显示欢迎信息
        self.statusBar().showMessage(f"欢迎使用威科夫点数图软件 V{__version__}", 5000)

    def _init_ui(self):
        """初始化UI布局"""
        # 设置窗口属性
        self.setWindowTitle(f"威科夫点数图软件 V{__version__}    软件功能支持定制，联系微信：zxzvsdcj")
        self.setMinimumSize(800, 600)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(True)
        self.tab_widget.setDocumentMode(True)
        
        # 隐藏标签栏以腾挪更多空间给点数图表
        self.tab_widget.tabBar().setVisible(False)

        # 创建点数图标签页
        self.point_figure_widget = PointFigureChartWidget(self.chart_controller)
        self.tab_widget.addTab(self.point_figure_widget, "点数图")

        # 添加标签页到主布局
        main_layout.addWidget(self.tab_widget)

        # 创建菜单栏
        self._setup_menubar()

        # 创建工具栏
        self._setup_toolbar()

        # 创建状态栏
        self._setup_statusbar()

    def _setup_menubar(self):
        """设置菜单栏"""
        # 创建菜单栏
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        # 打开点数图文件
        open_chart_action = QAction("打开点数图", self)
        open_chart_action.setShortcut("Ctrl+O")
        open_chart_action.triggered.connect(self.open_chart_file)
        file_menu.addAction(open_chart_action)

        # 保存点数图文件
        save_chart_action = QAction("保存点数图", self)
        save_chart_action.setShortcut("Ctrl+S")
        save_chart_action.triggered.connect(self.save_chart_file)
        file_menu.addAction(save_chart_action)

        # 分隔线
        file_menu.addSeparator()

        # 导入数据
        import_action = QAction("导入数据", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.show_import_dialog)
        file_menu.addAction(import_action)

        # 查看原始数据
        view_data_action = QAction("查看原始数据", self)
        view_data_action.setShortcut("Ctrl+D")
        view_data_action.triggered.connect(self.show_data_viewer)
        file_menu.addAction(view_data_action)

        # 导出数据
        export_action = QAction("导出数据", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        # 分隔线
        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)

        # 添加目标位测算快捷键说明
        target_shortcuts_action = QAction("目标位测算快捷键", self)
        target_shortcuts_action.triggered.connect(self.show_target_shortcuts_info)
        tools_menu.addAction(target_shortcuts_action)

        # 添加分隔符
        tools_menu.addSeparator()

        # 缩放功能子菜单
        zoom_menu = tools_menu.addMenu("缩放")
        
        # 放大
        zoom_in_action = QAction("放大", self)
        zoom_in_action.setShortcut("Ctrl++")
        zoom_in_action.triggered.connect(self._zoom_in)
        zoom_menu.addAction(zoom_in_action)
        
        # 缩小
        zoom_out_action = QAction("缩小", self)
        zoom_out_action.setShortcut("Ctrl+-")
        zoom_out_action.triggered.connect(self._zoom_out)
        zoom_menu.addAction(zoom_out_action)
        
        # 重置缩放
        zoom_reset_action = QAction("重置缩放", self)
        zoom_reset_action.setShortcut("Ctrl+0")
        zoom_reset_action.triggered.connect(self._zoom_reset)
        zoom_menu.addAction(zoom_reset_action)

        # 目标价测算子菜单
        target_menu = tools_menu.addMenu("目标价测算")
        
        # 上行目标
        up_target_action = QAction("上行目标", self)
        up_target_action.setShortcut("Ctrl+W")
        up_target_action.triggered.connect(self._calculate_upward_target)
        target_menu.addAction(up_target_action)
        
        # 下行目标
        down_target_action = QAction("下行目标", self)
        down_target_action.setShortcut("Ctrl+Z")
        down_target_action.triggered.connect(self._calculate_downward_target)
        target_menu.addAction(down_target_action)
        
        # 清除目标线
        clear_target_action = QAction("清除目标线", self)
        clear_target_action.triggered.connect(self._clear_target_overlays)
        target_menu.addAction(clear_target_action)
        
        target_menu.addSeparator()
        
        # 目标价位测算对话框
        target_dialog_action = QAction("目标价位测算对话框", self)
        target_dialog_action.triggered.connect(self._show_target_price_dialog)
        target_menu.addAction(target_dialog_action)

        # 趋势线管理
        trendline_action = QAction("趋势线管理", self)
        trendline_action.triggered.connect(self._manage_trendlines)
        tools_menu.addAction(trendline_action)

        # 支撑/阻力位分析
        sr_action = QAction("支撑/阻力位分析", self)
        sr_action.triggered.connect(self._mark_support_resistance)
        tools_menu.addAction(sr_action)

        # 成交量分布图
        distribution_action = QAction("成交量分布图", self)
        distribution_action.setShortcut("Ctrl+T")
        distribution_action.triggered.connect(self._show_distribution)
        tools_menu.addAction(distribution_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def _setup_toolbar(self):
        """设置工具栏"""
        # 创建工具栏
        toolbar = QToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # 不再添加导入数据按钮，将空间留给绘制图表
        # 如果需要导入数据，用户可以通过菜单栏的"文件">"导入数据"操作

    def _setup_statusbar(self):
        """设置状态栏"""
        # 创建状态栏
        statusbar = QStatusBar()
        self.setStatusBar(statusbar)

        # 添加导入状态标签（靠右显示）
        self.import_status_label = QLabel("数据导入状态")
        self.import_status_label.setStyleSheet("font-weight: bold; color: red;")
        self.import_status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        statusbar.addPermanentWidget(self.import_status_label)

    def _connect_signals(self):
        """连接信号"""
        # 在这里连接信号和槽
        pass

    def show_import_dialog(self):
        """显示导入数据对话框"""
        # 创建导入对话框
        dialog = DataImportDialog(self.data_controller, self)

        # 显示对话框
        if dialog.exec_():
            # 如果导入成功，更新点数图
            self.point_figure_widget.update_chart()

            # 更新右侧数据导入状态标签，而不是显示临时消息
            self.import_status_label.setText("数据导入成功")

    def export_data(self):
        """导出数据"""
        # 检查是否有数据
        if self.data_controller.get_data() is None:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return

        # 获取上次导出的目录
        last_export_dir = self.settings.get_setting("data", "last_export_directory", "")
        if not last_export_dir or not os.path.exists(last_export_dir):
            last_export_dir = os.path.expanduser("~/Documents")

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", last_export_dir, "CSV文件 (*.csv);;Excel文件 (*.xlsx)"
        )

        if file_path:
            # 保存当前目录作为下次导出的目录
            current_dir = os.path.dirname(file_path)
            self.settings.set_setting("data", "last_export_directory", current_dir)
            self.settings.save_settings()

            # 导出数据
            self.data_controller.export_data(file_path)

            # 显示成功消息
            self.statusBar().showMessage(f"数据已导出到 {file_path}", 3000)

    def open_chart_file(self):
        """打开点数图文件"""
        # 获取上次打开图表文件的目录
        last_chart_dir = self.settings.get_setting("data", "last_chart_directory", "")
        if not last_chart_dir or not os.path.exists(last_chart_dir):
            last_chart_dir = os.path.expanduser("~/Documents")

        # 选择文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开点数图", last_chart_dir, "点数图文件 (*.pfg);;所有文件 (*.*)"
        )

        if file_path:
            try:
                # 保存当前目录作为下次打开的目录
                current_dir = os.path.dirname(file_path)
                self.settings.set_setting("data", "last_chart_directory", current_dir)
                self.settings.save_settings()

                self.statusBar().showMessage(f"正在打开文件: {file_path}...")
                # 调用图表控制器的打开文件方法
                success = self.chart_controller.open_chart(file_path)

                if success:
                    # 更新点数图显示
                    self.point_figure_widget.update_chart()
                    self.statusBar().showMessage(f"成功打开文件: {file_path}", 3000)
                else:
                    QMessageBox.warning(self, "打开失败", "无法打开所选文件，文件格式可能不正确。")
                    self.statusBar().showMessage("打开文件失败", 3000)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"打开文件时出错: {str(e)}")
                self.statusBar().showMessage("打开文件出错", 3000)

    def save_chart_file(self):
        """保存点数图文件"""
        # 检查是否有图表数据
        if not self.chart_controller.has_chart():
            QMessageBox.warning(self, "无法保存", "没有可保存的图表数据。")
            return

        # 获取上次保存图表文件的目录
        last_chart_dir = self.settings.get_setting("data", "last_chart_directory", "")
        if not last_chart_dir or not os.path.exists(last_chart_dir):
            last_chart_dir = os.path.expanduser("~/Documents")

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存点数图", last_chart_dir, "点数图文件 (*.pfg);;所有文件 (*.*)"
        )

        if file_path:
            try:
                # 保存当前目录作为下次保存的目录
                current_dir = os.path.dirname(file_path)
                self.settings.set_setting("data", "last_chart_directory", current_dir)
                self.settings.save_settings()

                self.statusBar().showMessage(f"正在保存文件: {file_path}...")
                # 调用图表控制器的保存文件方法
                success = self.chart_controller.save_chart(file_path)

                if success:
                    self.statusBar().showMessage(f"成功保存文件: {file_path}", 3000)
                else:
                    QMessageBox.warning(self, "保存失败", "无法保存到所选位置。")
                    self.statusBar().showMessage("保存文件失败", 3000)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存文件时出错: {str(e)}")
                self.statusBar().showMessage("保存文件出错", 3000)

    def show_settings_dialog(self):
        """显示设置对话框"""
        # 创建设置对话框
        dialog = SettingsDialog(self)

        # 显示对话框
        if dialog.exec_():
            # 如果设置已更改，更新点数图
            self.point_figure_widget.update_settings()

    def show_about_dialog(self):
        """显示关于对话框"""
        # 创建关于对话框
        dialog = AboutDialog(self)

        # 显示对话框
        dialog.exec_()

    def show_target_shortcuts_info(self):
        """显示目标位测算快捷键说明"""
        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("目标位测算快捷键说明")
        dialog.setMinimumWidth(600)
        dialog.setMinimumHeight(400)

        # 创建布局
        layout = QVBoxLayout(dialog)

        # 创建文本说明
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h2>目标位测算快捷键说明</h2>
        <p>威科夫点数图软件提供了通过快捷键快速测算目标位的功能：</p>

        <h3>快捷键列表：</h3>
        <ul>
            <li><b>Ctrl+Q</b> 或 <b>Ctrl+W</b>：计算上行目标位</li>
            <li><b>Ctrl+Z</b> 或 <b>Ctrl+X</b>：计算下行目标位</li>
        </ul>

        <h3>使用方法：</h3>
        <ol>
            <li>在点数图表格中选择<b>一行中</b>的连续单元格区域</li>
            <li>按下相应快捷键计算目标位</li>
            <li>系统会自动计算并显示目标位结果：</li>
        </ol>

        <p>计算结果会在图表上显示目标位置线，其中：</p>
        <ul>
            <li>■ 数值：表示计算的最大目标值</li>
            <li>● 数值：表示计算的保守目标值</li>
        </ul>
        """)
        layout.addWidget(info_text)

        # 创建关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        # 显示对话框
        dialog.exec_()

    def eventFilter(self, watched, event):
        """事件过滤器，用于处理全局快捷键"""
        if event.type() == QEvent.KeyPress:
            print(f"[MainWindow] 捕获键盘事件: {event.key()}, 修饰键: {event.modifiers()}")

            # 检查是否是目标计算快捷键
            # 处理Ctrl+Shift组合键
            if event.modifiers() == (Qt.ControlModifier | Qt.ShiftModifier):
                # 下行目标计算：Ctrl+Shift+Z 或 Ctrl+Shift+X
                if event.key() in [Qt.Key_Z, Qt.Key_X]:
                    print("[MainWindow] 触发下行目标计算快捷键")
                    # 检查当前活动控件是否是点数图组件
                    if hasattr(self, 'point_figure_widget') and self.point_figure_widget:
                        self.point_figure_widget._calculate_downward_target()
                        return True

            # 处理仅Ctrl键组合
            elif event.modifiers() == Qt.ControlModifier:
                # 上行目标计算：Ctrl+Q 或 Ctrl+W
                if event.key() in [Qt.Key_Q, Qt.Key_W]:
                    print("[MainWindow] 触发上行目标计算快捷键")
                    # 检查当前活动控件是否是点数图组件
                    if hasattr(self, 'point_figure_widget') and self.point_figure_widget:
                        self.point_figure_widget._calculate_upward_target()
                        return True

        # 未处理的事件交给父类处理
        return super().eventFilter(watched, event)

    def show_data_viewer(self):
        """显示数据查看器"""
        # 检查是否有数据
        if self.data_controller.get_data() is None:
            QMessageBox.warning(self, "警告", "没有可查看的数据")
            return

        # 创建数据查看器对话框
        dialog = DataViewerDialog(self.data_controller, self)

        # 显示对话框
        dialog.exec_()

    # 添加点数图功能的菜单槽函数
    def _zoom_in(self):
        """放大点数图"""
        if hasattr(self.point_figure_widget, '_zoom_in'):
            self.point_figure_widget._zoom_in()

    def _zoom_out(self):
        """缩小点数图"""
        if hasattr(self.point_figure_widget, '_zoom_out'):
            self.point_figure_widget._zoom_out()

    def _zoom_reset(self):
        """重置点数图缩放"""
        if hasattr(self.point_figure_widget, 'zoom_level'):
            self.point_figure_widget.zoom_level = 100
            if hasattr(self.point_figure_widget, '_apply_zoom'):
                self.point_figure_widget._apply_zoom()

    def _calculate_upward_target(self):
        """计算上行目标"""
        if hasattr(self.point_figure_widget, '_calculate_upward_target'):
            self.point_figure_widget._calculate_upward_target()

    def _calculate_downward_target(self):
        """计算下行目标"""
        if hasattr(self.point_figure_widget, '_calculate_downward_target'):
            self.point_figure_widget._calculate_downward_target()

    def _clear_target_overlays(self):
        """清除目标线"""
        if hasattr(self.point_figure_widget, '_explicit_clear_target_overlays'):
            self.point_figure_widget._explicit_clear_target_overlays()

    def _show_target_price_dialog(self):
        """显示目标价位测算对话框"""
        if hasattr(self.point_figure_widget, 'show_target_price_dialog'):
            self.point_figure_widget.show_target_price_dialog()

    def _manage_trendlines(self):
        """管理趋势线"""
        if hasattr(self.point_figure_widget, 'manage_trendlines'):
            self.point_figure_widget.manage_trendlines()

    def _mark_support_resistance(self):
        """标记支撑/阻力位"""
        if hasattr(self.point_figure_widget, 'mark_support_resistance'):
            self.point_figure_widget.mark_support_resistance()

    def _show_distribution(self):
        """显示成交量分布图"""
        if hasattr(self.point_figure_widget, '_on_distribution_button_clicked'):
            self.point_figure_widget._on_distribution_button_clicked()