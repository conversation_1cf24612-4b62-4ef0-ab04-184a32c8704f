#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图显示组件
用于显示威科夫点数图
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import platform
import time  # 添加time模块用于防抖处理
import logging
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvas, NavigationToolbar2QT as NavigationToolbar
import matplotlib

# 配置matplotlib中文字体
try:
    # 尝试使用系统中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像负号'-'显示为方块的问题
    matplotlib.rc('font', family='sans-serif')
except Exception as e:
    print(f"配置matplotlib中文字体出错: {e}")

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QGroupBox,
    QSpinBox, QDoubleSpinBox, QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QMessageBox, QColorDialog, QSplitter, QAbstractItemView, QSizePolicy, QMenu, QAction,
    QSlider, QRadioButton, QCheckBox, QDialogButtonBox, QDialog, QListWidget, QListWidgetItem,
    QApplication, QStyle, QStyledItemDelegate, QStyleOptionViewItem, QGridLayout, QFormLayout,
    QScrollArea, QTableWidgetSelectionRange, QFileDialog, QButtonGroup, QProgressDialog,
    QLineEdit, QDateEdit, QTextEdit, QMainWindow, QInputDialog  # 添加QInputDialog
)
from PyQt5.QtCore import (
    Qt, QTimer, QObject, QSize, pyqtSignal, QRect, QRectF, QPoint, QPointF, QEvent, QMargins, QVariant, pyqtSlot
)
from PyQt5.QtGui import (
    QColor, QPen, QBrush, QPainter, QFont, QIcon, QPixmap, QImage, QResizeEvent, QPalette, QCursor, QFontMetrics
)

# 使用基于主目录的相对导入
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from controllers.chart_controller import ChartController
from controllers.data_controller import DataController
from utils.settings_manager import SettingsManager
from ui.volume_distribution_overlay import VolumeDistributionOverlay

# 创建logger
logger = logging.getLogger(__name__)


class TargetPriceDialog(QDialog):
    """目标价位测算对话框"""

    def __init__(self, chart_data, box_size, reversal_amount, parent=None):
        """初始化目标价位测算对话框"""
        super().__init__(parent)

        # 保存原始数据
        self.original_chart_data = chart_data

        # 预处理数据 - 确保chart_data的索引是数值类型
        try:
            # 创建chart_data的副本
            processed_data = chart_data.copy()

            # 确保索引是数值类型
            numeric_indices = []
            for idx in processed_data.index:
                if isinstance(idx, str):
                    try:
                        numeric_indices.append(float(idx))
                    except (ValueError, TypeError):
                        # 如果无法转换，保留原始值（后续会跳过）
                        numeric_indices.append(idx)
                else:
                    numeric_indices.append(idx)

            # 设置新的索引
            processed_data.index = numeric_indices

            # 过滤出只包含数值类型的索引
            numeric_only = processed_data[processed_data.index.map(lambda x: isinstance(x, (int, float)))]

            # 如果过滤后非空，使用过滤后的数据
            if not numeric_only.empty:
                self.chart_data = numeric_only
            else:
                # 否则使用原始数据
                self.chart_data = processed_data
                print("警告: 过滤后的数据为空，使用原始数据")
        except Exception as e:
            # 如果预处理失败，使用原始数据
            self.chart_data = chart_data
            print(f"预处理数据时出错: {str(e)}")

        self.box_size = box_size
        self.reversal_amount = reversal_amount

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("目标价位测算")
        self.setMinimumWidth(450)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建计算方法选择组
        method_group = QGroupBox("计算方法")
        method_layout = QVBoxLayout(method_group)

        self.method_group = QButtonGroup(self)

        # 水平计数法
        self.horizontal_radio = QRadioButton("水平计数法（横盘宽度计算）")
        self.horizontal_radio.setChecked(True)
        self.method_group.addButton(self.horizontal_radio)
        method_layout.addWidget(self.horizontal_radio)

        # 垂直计数法
        self.vertical_radio = QRadioButton("垂直计数法（趋势高度计算）")
        self.method_group.addButton(self.vertical_radio)
        method_layout.addWidget(self.vertical_radio)

        # 添加计算方法说明
        method_desc = QLabel("水平计数法适用于横盘突破，垂直计数法适用于趋势延续")
        method_desc.setWordWrap(True)
        method_layout.addWidget(method_desc)

        # 添加到主布局
        main_layout.addWidget(method_group)

        # 创建表单布局
        form_layout = QFormLayout()

        # 创建箱体类型选择
        pattern_group = QGroupBox("箱体类型")
        pattern_layout = QVBoxLayout(pattern_group)

        self.pattern_group = QButtonGroup(self)

        # 吸筹形态（底部区域）
        self.accumulation_radio = QRadioButton("吸筹形态（底部区域）")
        self.accumulation_radio.setChecked(True)
        self.pattern_group.addButton(self.accumulation_radio)

        # 派发形态（顶部区域）
        self.distribution_radio = QRadioButton("派发形态（顶部区域）")
        self.pattern_group.addButton(self.distribution_radio)

        # 创建箱体类型布局
        pattern_layout.addWidget(self.accumulation_radio)
        pattern_layout.addWidget(self.distribution_radio)

        # 添加到主布局
        main_layout.addWidget(pattern_group)

        # 创建起始位置输入
        self.start_pos_spin = QSpinBox()
        self.start_pos_spin.setRange(1, 100)
        self.start_pos_spin.setValue(1)
        self.start_pos_spin.valueChanged.connect(self._update_selection)
        form_layout.addRow("起始位置:", self.start_pos_spin)

        # 创建结束位置输入
        self.end_pos_spin = QSpinBox()
        self.end_pos_spin.setRange(1, 100)
        self.end_pos_spin.setValue(10)
        self.end_pos_spin.valueChanged.connect(self._update_selection)
        form_layout.addRow("结束位置:", self.end_pos_spin)

        # 创建参数显示区域
        params_group = QGroupBox("计算参数")
        params_layout = QFormLayout(params_group)

        # 显示箱体大小
        self.box_size_label = QLabel(f"{self.box_size}")
        params_layout.addRow("箱体大小:", self.box_size_label)

        # 显示反转单位
        self.reversal_label = QLabel(f"{self.reversal_amount}")
        params_layout.addRow("反转单位:", self.reversal_label)

        # 创建箱体宽度显示
        self.width_label = QLabel("0")
        params_layout.addRow("箱体宽度:", self.width_label)

        # 创建箱体高度显示
        self.height_label = QLabel("0")
        params_layout.addRow("箱体高度:", self.height_label)

        # 创建目标价位显示
        self.target_label = QLabel("0")
        params_layout.addRow("目标价位:", self.target_label)

        # 添加参数组到主布局
        main_layout.addWidget(params_group)

        # 添加表单布局到主布局
        main_layout.addLayout(form_layout)

        # 创建按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # 添加计算按钮
        calculate_button = QPushButton("计算")
        calculate_button.clicked.connect(self._calculate_target)
        button_box.addButton(calculate_button, QDialogButtonBox.ActionRole)

        # 添加按钮到主布局
        main_layout.addWidget(button_box)

        # 更新选择
        self._update_selection()

    def _update_selection(self):
        """更新选择"""
        # 确保结束位置大于起始位置
        if self.end_pos_spin.value() <= self.start_pos_spin.value():
            self.end_pos_spin.setValue(self.start_pos_spin.value() + 1)

        # 计算并显示宽度
        width = abs(self.end_pos_spin.value() - self.start_pos_spin.value()) + 1
        self.width_label.setText(f"{width} 列")

    def _calculate_target(self):
        """计算目标价位"""
        try:
            # 数据预处理 - 确保chart_data的索引是数值类型
            try:
                # 创建chart_data的副本，避免修改原始数据
                processed_data = self.chart_data.copy()

                # 确保索引是数值类型
                numeric_indices = []
                for idx in processed_data.index:
                    if isinstance(idx, str):
                        try:
                            numeric_indices.append(float(idx))
                        except (ValueError, TypeError):
                            # 如果无法转换，保留原始值（后续会跳过）
                            numeric_indices.append(idx)
                    else:
                        numeric_indices.append(idx)

                # 设置新的索引
                processed_data.index = numeric_indices

                # 过滤出只包含数值类型的索引
                numeric_only = processed_data[processed_data.index.map(lambda x: isinstance(x, (int, float)))]

                # 如果过滤后为空，弹出警告
                if numeric_only.empty:
                    QMessageBox.warning(self, "警告", "图表数据中没有有效的数值数据")
                    return

                # 使用过滤后的数据代替原始数据
                processed_data = numeric_only
            except Exception as e:
                print(f"预处理数据时出错: {str(e)}")
                # 如果预处理失败，继续使用原始数据
                processed_data = self.chart_data

            # 获取起始和结束位置
            start_pos = self.start_pos_spin.value() - 1  # 转换为0索引
            end_pos = self.end_pos_spin.value() - 1  # 转换为0索引

            # 检查位置是否有效
            if start_pos < 0 or end_pos >= len(processed_data.columns):
                QMessageBox.warning(self, "警告", "位置超出范围")
                return

            # 获取计算方法
            is_horizontal_method = self.horizontal_radio.isChecked()

            # 获取箱体类型
            is_accumulation = self.accumulation_radio.isChecked()

            # 直接使用价格索引进行计算，而不是尝试解析X和O符号
            # 获取所选区域的所有价格值（索引）
            prices_in_range = []
            for col_idx in range(start_pos, end_pos + 1):
                col = processed_data.iloc[:, col_idx]
                # 只收集有值的行（有X或O符号的位置）
                for idx, val in col.items():
                    if pd.notna(val):  # 如果单元格有值（X或O）
                        # 确保索引是数值类型
                        if isinstance(idx, (int, float)):
                            prices_in_range.append(idx)
                        elif isinstance(idx, str):
                            try:
                                prices_in_range.append(float(idx))
                            except (ValueError, TypeError):
                                continue

            # 确保有足够的数据进行计算
            if not prices_in_range:
                QMessageBox.warning(self, "警告",
                                    "所选区域没有有效的价格数据。\n\n"
                                    "请选择包含X或O符号的区域，且确保区域足够大，包含足够的数据点。\n\n"
                                    "建议:\n"
                                    "1. 选择一个更大的区域\n"
                                    "2. 确保选择了正确的箱体类型(吸筹/派发)\n"
                                    "3. 尝试选择图表中有明显形态的区域")
                return

            # 水平计数法 - 基于横盘宽度
            if is_horizontal_method:
                # 计算箱体宽度（列数）
                width = abs(end_pos - start_pos) + 1  # +1是因为包含起始列

                # 计算箱体高度
                if is_accumulation:
                    # 吸筹形态：价格区间是底部区域
                    min_price = min(prices_in_range)
                    max_price = max(prices_in_range)
                    height = max_price - min_price

                    # 突破价是箱体顶部价格
                    breakout_price = max_price

                    # 目标价位 = 突破点价格 + (列数 × 箱体大小 × 反转单位)
                    target_price = breakout_price + (width * self.box_size * self.reversal_amount)
                else:
                    # 派发形态：价格区间是顶部区域
                    min_price = min(prices_in_range)
                    max_price = max(prices_in_range)
                    height = max_price - min_price

                    # 突破价是箱体底部价格
                    breakout_price = min_price

                    # 目标价位 = 突破点价格 - (列数 × 箱体大小 × 反转单位)
                    target_price = breakout_price - (width * self.box_size * self.reversal_amount)

                # 更新显示
                self.height_label.setText(f"{height:.2f} ({int(height / self.box_size)}格)")
                self.width_label.setText(f"{width} 列")

            # 垂直计数法 - 基于垂直高度
            else:
                # 获取起始和结束列的价格范围
                start_prices = []
                end_prices = []

                # 收集起始列的价格
                start_col = processed_data.iloc[:, start_pos]
                for idx, val in start_col.items():
                    if pd.notna(val):  # 有X或O符号
                        if isinstance(idx, (int, float)):
                            start_prices.append(idx)
                        elif isinstance(idx, str):
                            try:
                                start_prices.append(float(idx))
                            except (ValueError, TypeError):
                                continue

                # 收集结束列的价格
                end_col = processed_data.iloc[:, end_pos]
                for idx, val in end_col.items():
                    if pd.notna(val):  # 有X或O符号
                        if isinstance(idx, (int, float)):
                            end_prices.append(idx)
                        elif isinstance(idx, str):
                            try:
                                end_prices.append(float(idx))
                            except (ValueError, TypeError):
                                continue

                # 确保有足够的数据进行计算
                if not start_prices or not end_prices:
                    QMessageBox.warning(self, "警告", "起始列或结束列没有有效的价格数据")
                    return

                # 计算垂直高度
                if is_accumulation:
                    # 上涨趋势：从起始列的最低价到结束列的最高价
                    start_price = min(start_prices)
                    end_price = max(end_prices)

                    # 计算垂直高度（单方向价格运动的格数）
                    height = abs(end_price - start_price)
                    height_boxes = int(height / self.box_size)

                    # 目标价位 = 起点价格 + (数量 × 箱体大小 × 反转单位)
                    target_price = start_price + (height_boxes * self.box_size * self.reversal_amount)
                else:
                    # 下跌趋势：从起始列的最高价到结束列的最低价
                    start_price = max(start_prices)
                    end_price = min(end_prices)

                    # 计算垂直高度（单方向价格运动的格数）
                    height = abs(start_price - end_price)
                    height_boxes = int(height / self.box_size)

                    # 目标价位 = 起点价格 - (数量 × 箱体大小 × 反转单位)
                    target_price = start_price - (height_boxes * self.box_size * self.reversal_amount)

                # 更新显示
                self.height_label.setText(f"{height:.2f} ({height_boxes}格)")
                width = abs(end_pos - start_pos) + 1
                self.width_label.setText(f"{width} 列")

            # 更新目标价位显示
            self.target_label.setText(f"{target_price:.2f}")

        except Exception as e:
            error_msg = f"计算过程中发生错误：{str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "计算错误", error_msg)
            import traceback
            traceback.print_exc()


class TrendlineDialog(QDialog):
    """趋势线设置对话框"""

    def __init__(self, parent=None, existing_trendline=None):
        """
        初始化趋势线设置对话框

        参数:
            parent: 父窗口
            existing_trendline: 现有趋势线数据(用于编辑)
        """
        super().__init__(parent)

        self.existing_trendline = existing_trendline
        self._init_ui()

        # 如果是编辑模式，加载现有数据
        if existing_trendline:
            self._load_existing_data()

    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("趋势线设置")
        self.setMinimumWidth(350)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建表单布局
        form_layout = QFormLayout()

        # 趋势线名称
        self.name_edit = QLineEdit()
        if not self.existing_trendline:  # 新建模式
            self.name_edit.setText("趋势线 " + time.strftime("%H:%M:%S"))
        form_layout.addRow("名称:", self.name_edit)

        # 趋势线类型
        self.type_combo = QComboBox()
        self.type_combo.addItems(["上升趋势", "下降趋势", "水平支撑", "水平阻力"])
        form_layout.addRow("类型:", self.type_combo)

        # 起始位置
        self.start_col_spin = QSpinBox()
        self.start_col_spin.setRange(1, 1000)
        self.start_col_spin.setValue(1)
        form_layout.addRow("起始列:", self.start_col_spin)

        self.start_price_spin = QDoubleSpinBox()
        self.start_price_spin.setRange(0, 1000000)
        self.start_price_spin.setDecimals(2)
        self.start_price_spin.setValue(100)
        form_layout.addRow("起始价格:", self.start_price_spin)

        # 结束位置
        self.end_col_spin = QSpinBox()
        self.end_col_spin.setRange(1, 1000)
        self.end_col_spin.setValue(10)
        form_layout.addRow("结束列:", self.end_col_spin)

        self.end_price_spin = QDoubleSpinBox()
        self.end_price_spin.setRange(0, 1000000)
        self.end_price_spin.setDecimals(2)
        self.end_price_spin.setValue(110)
        form_layout.addRow("结束价格:", self.end_price_spin)

        # 线条颜色
        self.color_button = QPushButton()
        self.color_button.setFixedSize(24, 24)
        self.current_color = QColor(0, 128, 0)  # 默认绿色
        self.color_button.setStyleSheet(f"background-color: {self.current_color.name()}; border: 1px solid #888;")
        self.color_button.clicked.connect(self._choose_color)
        form_layout.addRow("颜色:", self.color_button)

        # 线条宽度
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 5)
        self.width_spin.setValue(2)
        form_layout.addRow("线宽:", self.width_spin)

        # 线条样式
        self.style_combo = QComboBox()
        self.style_combo.addItems(["实线", "虚线", "点线", "点划线"])
        form_layout.addRow("样式:", self.style_combo)

        # 添加表单布局到主布局
        main_layout.addLayout(form_layout)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def _choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(self.current_color, self, "选择趋势线颜色")
        if color.isValid():
            self.current_color = color
            self.color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")

    def _load_existing_data(self):
        """加载现有趋势线数据"""
        if not self.existing_trendline:
            return

        # 加载名称
        self.name_edit.setText(self.existing_trendline['name'])

        # 加载类型
        type_index = ["上升趋势", "下降趋势", "水平支撑", "水平阻力"].index(self.existing_trendline['type'])
        self.type_combo.setCurrentIndex(type_index)

        # 加载起始位置
        self.start_col_spin.setValue(self.existing_trendline['start_col'])
        self.start_price_spin.setValue(self.existing_trendline['start_price'])

        # 加载结束位置
        self.end_col_spin.setValue(self.existing_trendline['end_col'])
        self.end_price_spin.setValue(self.existing_trendline['end_price'])

        # 加载颜色
        self.current_color = QColor(self.existing_trendline['color'])
        self.color_button.setStyleSheet(f"background-color: {self.current_color.name()}; border: 1px solid #888;")

        # 加载线宽
        self.width_spin.setValue(self.existing_trendline['width'])

        # 加载样式
        style_index = ["实线", "虚线", "点线", "点划线"].index(self.existing_trendline['style'])
        self.style_combo.setCurrentIndex(style_index)

    def get_trendline_data(self):
        """获取趋势线数据"""
        return {
            'name': self.name_edit.text(),
            'type': self.type_combo.currentText(),
            'start_col': self.start_col_spin.value(),
            'start_price': self.start_price_spin.value(),
            'end_col': self.end_col_spin.value(),
            'end_price': self.end_price_spin.value(),
            'color': self.current_color.name(),
            'width': self.width_spin.value(),
            'style': self.style_combo.currentText()
        }


class TrendlineManagerDialog(QDialog):
    """趋势线管理对话框"""

    def __init__(self, trendlines, parent=None):
        """
        初始化趋势线管理对话框

        参数:
            trendlines: 趋势线列表
            parent: 父窗口
        """
        super().__init__(parent)

        self.trendlines = trendlines.copy()  # 创建副本，避免直接修改原列表
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("趋势线管理")
        self.setMinimumSize(400, 300)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建趋势线列表
        self.list_widget = QListWidget()
        self.list_widget.itemSelectionChanged.connect(self._update_buttons)
        main_layout.addWidget(self.list_widget)

        # 填充趋势线列表
        self._fill_list()

        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 添加按钮
        self.add_button = QPushButton("添加")
        self.add_button.clicked.connect(self._add_trendline)
        button_layout.addWidget(self.add_button)

        # 编辑按钮
        self.edit_button = QPushButton("编辑")
        self.edit_button.clicked.connect(self._edit_trendline)
        self.edit_button.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.edit_button)

        # 删除按钮
        self.delete_button = QPushButton("删除")
        self.delete_button.clicked.connect(self._delete_trendline)
        self.delete_button.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.delete_button)

        # 添加按钮布局到主布局
        main_layout.addLayout(button_layout)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def _fill_list(self):
        """填充趋势线列表"""
        self.list_widget.clear()

        for trendline in self.trendlines:
            item = QListWidgetItem(f"{trendline['name']} ({trendline['type']})")
            item.setData(Qt.UserRole, trendline)  # 存储趋势线数据
            self.list_widget.addItem(item)

    def _update_buttons(self):
        """更新按钮状态"""
        has_selection = len(self.list_widget.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

    def _add_trendline(self):
        """添加趋势线"""
        dialog = TrendlineDialog(self)

        if dialog.exec_():
            # 获取趋势线数据
            trendline_data = dialog.get_trendline_data()

            # 添加到列表
            self.trendlines.append(trendline_data)

            # 更新列表
            self._fill_list()

    def _edit_trendline(self):
        """编辑趋势线"""
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            return

        # 获取选中的趋势线
        selected_item = selected_items[0]
        selected_index = self.list_widget.row(selected_item)
        trendline_data = selected_item.data(Qt.UserRole)

        # 显示编辑对话框
        dialog = TrendlineDialog(self, trendline_data)

        if dialog.exec_():
            # 更新趋势线数据
            self.trendlines[selected_index] = dialog.get_trendline_data()

            # 更新列表
            self._fill_list()

    def _delete_trendline(self):
        """删除趋势线"""
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            return

        # 获取选中的趋势线
        selected_item = selected_items[0]
        selected_index = self.list_widget.row(selected_item)

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除趋势线 \"{selected_item.text()}\" 吗?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从列表中删除
            del self.trendlines[selected_index]

            # 更新列表
            self._fill_list()

    def get_trendlines(self):
        """获取趋势线列表"""
        return self.trendlines


class SupportResistanceDialog(QDialog):
    """支撑/阻力位设置对话框"""

    def __init__(self, chart_data, box_size, parent=None):
        """
        初始化支撑/阻力位设置对话框

        参数:
            chart_data: 点数图数据
            box_size: 格子大小
            parent: 父窗口
        """
        super().__init__(parent)

        self.chart_data = chart_data
        self.box_size = box_size
        self.support_resistance_levels = []

        self._init_ui()
        self._detect_levels()

    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("支撑/阻力位标记")
        self.setMinimumSize(450, 400)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建说明标签
        help_label = QLabel(
            "系统已自动检测出可能的支撑/阻力位。您可以勾选需要显示的位置，"
            "也可以手动添加新的支撑/阻力位。"
        )
        help_label.setWordWrap(True)
        main_layout.addWidget(help_label)

        # 创建列表
        self.level_list = QTableWidget()
        self.level_list.setColumnCount(4)
        self.level_list.setHorizontalHeaderLabels(["选择", "价格", "类型", "强度"])
        self.level_list.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.level_list.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # 设置列宽
        self.level_list.setColumnWidth(0, 50)  # 选择列
        self.level_list.setColumnWidth(1, 100)  # 价格列
        self.level_list.setColumnWidth(2, 100)  # 类型列
        self.level_list.setColumnWidth(3, 100)  # 强度列

        main_layout.addWidget(self.level_list)

        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 添加按钮
        add_button = QPushButton("添加自定义位置")
        add_button.clicked.connect(self._add_custom_level)
        button_layout.addWidget(add_button)

        # 删除按钮
        delete_button = QPushButton("删除")
        delete_button.clicked.connect(self._delete_level)
        button_layout.addWidget(delete_button)

        # 添加按钮布局到主布局
        main_layout.addLayout(button_layout)

        # 添加确定和取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def _detect_levels(self):
        """自动检测支撑/阻力位"""
        if self.chart_data is None or self.chart_data.empty:
            return

        # 获取所有价格
        prices = self.chart_data.index.tolist()

        # 遍历每一行，计算每个价格水平的"接触次数"
        price_touches = {}

        for price in prices:
            price_row = self.chart_data.loc[price]

            # 计算非空值的数量（即X或O符号的存在）
            touch_count = price_row.count()

            if touch_count > 0:
                price_touches[price] = touch_count

        # 计算接触次数的平均值和标准差，用于判断强度
        if price_touches:
            avg_touches = sum(price_touches.values()) / len(price_touches)

            # 选取接触次数高于平均值的价格作为候选支撑/阻力位
            for price, touches in price_touches.items():
                if touches > avg_touches:
                    # 判断是支撑位还是阻力位
                    level_type = self._determine_level_type(price)

                    # 计算强度（基于接触次数与平均值的比率）
                    strength = touches / avg_touches
                    strength_text = "弱"
                    if strength > 1.5:
                        strength_text = "强"
                    elif strength > 1.2:
                        strength_text = "中"

                    # 添加到列表
                    self.support_resistance_levels.append({
                        'price': price,
                        'type': level_type,
                        'strength': strength_text,
                        'selected': True  # 默认选中
                    })

        # 按价格排序
        self.support_resistance_levels.sort(key=lambda x: x['price'], reverse=True)

        # 更新列表显示
        self._update_level_list()

    def _determine_level_type(self, price):
        """判断价格是支撑位还是阻力位"""
        if self.chart_data is None or self.chart_data.empty:
            return "未知"

        # 获取该价格行
        price_row = self.chart_data.loc[price]

        # 计算该行的X和O的数量
        x_count = 0
        o_count = 0

        for value in price_row:
            if value == 'X':
                x_count += 1
            elif value == 'O':
                o_count += 1

        # 根据X和O的比例判断
        if x_count > o_count * 1.5:
            return "支撑位"  # X多表示支撑（上涨止跌）
        elif o_count > x_count * 1.5:
            return "阻力位"  # O多表示阻力（下跌止涨）
        else:
            return "支撑/阻力"  # 两者接近，可能同时是支撑和阻力

    def _update_level_list(self):
        """更新支撑/阻力位列表"""
        self.level_list.setRowCount(len(self.support_resistance_levels))

        for row, level in enumerate(self.support_resistance_levels):
            # 创建选择框
            checkbox = QTableWidgetItem()
            checkbox.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            if level['selected']:
                checkbox.setCheckState(Qt.Checked)
            else:
                checkbox.setCheckState(Qt.Unchecked)
            self.level_list.setItem(row, 0, checkbox)

            # 添加价格
            price_item = QTableWidgetItem(f"{level['price']:.2f}")
            self.level_list.setItem(row, 1, price_item)

            # 添加类型
            type_item = QTableWidgetItem(level['type'])
            self.level_list.setItem(row, 2, type_item)

            # 添加强度
            strength_item = QTableWidgetItem(level['strength'])
            self.level_list.setItem(row, 3, strength_item)

    def _add_custom_level(self):
        """添加自定义支撑/阻力位"""
        # 获取价格范围
        if self.chart_data is None or self.chart_data.empty:
            min_price = 0
            max_price = 1000
        else:
            prices = self.chart_data.index.tolist()
            min_price = min(prices)
            max_price = max(prices)

        # 显示价格输入对话框
        price, ok = QInputDialog.getDouble(
            self, "输入价格", "请输入价格:",
            min(max_price, 100.0), min_price, max_price, 2
        )

        if ok:
            # 显示类型选择对话框
            type_options = ["支撑位", "阻力位", "支撑/阻力"]
            level_type, ok = QInputDialog.getItem(
                self, "选择类型", "请选择类型:",
                type_options, 0, False
            )

            if ok:
                # 显示强度选择对话框
                strength_options = ["弱", "中", "强"]
                strength, ok = QInputDialog.getItem(
                    self, "选择强度", "请选择强度:",
                    strength_options, 1, False
                )

                if ok:
                    # 添加到列表
                    self.support_resistance_levels.append({
                        'price': price,
                        'type': level_type,
                        'strength': strength,
                        'selected': True
                    })

                    # 按价格排序
                    self.support_resistance_levels.sort(key=lambda x: x['price'], reverse=True)

                    # 更新列表显示
                    self._update_level_list()

    def _delete_level(self):
        """删除选中的支撑/阻力位"""
        selected_rows = self.level_list.selectedIndexes()
        if not selected_rows:
            return

        # 获取选中的行
        row = selected_rows[0].row()

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除价格为 {self.support_resistance_levels[row]['price']:.2f} 的{self.support_resistance_levels[row]['type']}吗?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从列表中删除
            del self.support_resistance_levels[row]

            # 更新列表显示
            self._update_level_list()

    def get_selected_levels(self):
        """获取选中的支撑/阻力位"""
        selected_levels = []

        for row in range(self.level_list.rowCount()):
            checkbox = self.level_list.item(row, 0)
            if checkbox.checkState() == Qt.Checked:
                level = self.support_resistance_levels[row].copy()
                level['selected'] = True
                selected_levels.append(level)

        return selected_levels


class TargetLineDelegate(QStyledItemDelegate):
    """自定义委托用于绘制目标线和高亮"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.target_lines = None
        self.price_map = {}  # 行索引到价格的映射
        self.row_height = 20  # 默认行高

    def set_target_lines(self, target_lines, price_map=None, row_height=None):
        """设置目标线数据"""
        self.target_lines = target_lines
        if price_map:
            self.price_map = price_map
        if row_height:
            self.row_height = row_height

    def paint(self, painter, option, index):
        """绘制单元格，可以在这里处理特殊的绘制需求"""
        # 先让默认委托绘制基础内容
        super().paint(painter, option, index)

        # 如果没有目标线数据，直接返回
        if not self.target_lines:
            return

        # 获取行索引和矩形区域
        row = index.row()
        rect = option.rect

        # 检查这一行是否是目标价格行
        is_target_row = False
        row_price = None

        # 从价格映射中获取当前行的价格
        if row in self.price_map:
            row_price = self.price_map[row]

            # 检查是否是目标价格行
            min_target = self.target_lines.get('min')
            max_target = self.target_lines.get('max')
            is_upward = self.target_lines.get('is_upward', True)

            # 处理精确匹配或近似匹配
            if min_target is not None and max_target is not None:
                # 允许误差范围
                tolerance = self.row_height / 100.0  # 根据行高自适应误差

                # 检查是否在目标价格附近
                if abs(row_price - min_target) < tolerance or abs(row_price - max_target) < tolerance:
                    is_target_row = True

                    # 设置绘制风格
                    pen_color = QColor("green") if is_upward else QColor("red")
                    if abs(row_price - min_target) < tolerance:
                        pen_style = Qt.DotLine
                    else:
                        pen_style = Qt.DashLine

                    # 绘制水平线
                    pen = QPen(pen_color, 2, pen_style)
                    painter.save()
                    painter.setPen(pen)

                    # 获取表格宽度
                    parent = self.parent()
                    if parent:
                        width = parent.width()
                    else:
                        width = rect.width() * 10  # 估计宽度

                    # 绘制中心线
                    y = rect.center().y()
                    painter.drawLine(0, y, width, y)

                    # 恢复画笔状态
                    painter.restore()


class PointFigureChartWidget(QWidget):
    """点数图显示组件"""

    def __init__(self, chart_controller):
        """初始化点数图组件"""
        super().__init__()
        self.chart_controller = chart_controller
        self.chart_data = None

        # 从chart_controller加载配置，而不是使用硬编码默认值
        self.box_size = self.chart_controller.box_size
        self.reversal_amount = self.chart_controller.reversal_amount

        # 将控制器中的method转换为UI显示的文本
        if self.chart_controller.method == "high_low":
            self.method = "高低价法"
        elif self.chart_controller.method == "close":
            self.method = "收盘价法"
        else:
            self.method = "高低价法"  # 默认值

        self.current_scale = 1.0
        self.zoom_min = 0.5
        self.zoom_max = 2.0
        self.zoom_step = 0.1
        self.zoom_level = 100  # 添加zoom_level属性初始化
        self.zoom_disabled = False  # 控制是否禁用缩放的标志
        self.default_row_height = 20
        self.default_col_width = 20
        self.volume_chart_height = 100
        self.volume_data = None

        # 加载成交量相关设置
        self.volume_scale = self.chart_controller.settings.get_setting("chart", "volume_scale", 500.0)
        # 修复：确保成交量显示设置的一致性，默认为False以匹配用户期望
        self.show_volume_chart = self.chart_controller.settings.get_setting("ui", "show_volume_chart", False)

        # 从设置中加载Web视图缩放级别
        self.zoom_level = self.chart_controller.settings.get_setting("web_view", "zoom_level", 100)
        print(f"[PointFigureChart] 初始化时加载缩放级别: {self.zoom_level}")

        self.support_resistance_levels = []
        self.trendlines = []
        self.hover_info = None
        self.target_lines = None
        self.table_widget = None
        self.volume_table = None
        self.price_labels = []

        # 从控制器加载自动格值设置
        self.auto_box_size = self.chart_controller.settings.get_setting("chart", "auto_box_size", True)

        # 添加防止重复绘制的标记
        self.drawing_target_lines = False
        import time
        self.last_draw_time = time.time() - 10  # 初始化为10秒前，确保第一次绘制不受阻

        # 添加鼠标信息显示相关变量
        self.hover_cell = None
        self.hover_info_label = None

        # 添加缩放防抖定时器
        self.zoom_debounce = 200  # 200毫秒防抖
        self.zoom_timer = QTimer()
        self.zoom_timer.setSingleShot(True)
        self.zoom_timer.timeout.connect(self._apply_zoom_debounced)

        # 从控制器加载UI设置
        self.settings = {
            # 从控制器设置加载，使用合理的默认值
            "background_color": self.chart_controller.settings.get_setting("ui", "background_color", "#F0F0F0"),
            "grid_color": self.chart_controller.settings.get_setting("ui", "chart_grid_color", "#D0D0D0"),
            "x_color": self.chart_controller.settings.get_setting("ui", "chart_x_color", "#0000FF"),
            "o_color": self.chart_controller.settings.get_setting("ui", "chart_o_color", "#FF0000"),
            "grid_style": Qt.SolidLine,  # 网格线样式直接使用Qt常量
            "volume_up_color": self.chart_controller.settings.get_setting("ui", "volume_up_color", "#00AA00"),
            "volume_down_color": self.chart_controller.settings.get_setting("ui", "volume_down_color", "#AA0000"),
            "show_volume": self.show_volume_chart  # 使用已加载的成交量显示设置
        }

        # 初始化颜色属性
        self.background_color = QColor(self.settings["background_color"])
        self.grid_color = QColor(self.settings["grid_color"])
        self.x_symbol_color = QColor(self.settings["x_color"])
        self.o_symbol_color = QColor(self.settings["o_color"])
        self.grid_style = self.settings["grid_style"]
        self.volume_up_color = QColor(self.settings["volume_up_color"])
        self.volume_down_color = QColor(self.settings["volume_down_color"])

        # 初始化价格列颜色属性
        self.price_bg_color = QColor(self.chart_controller.settings.get_setting("ui", "price_bg_color", "#E0E0E0"))
        self.price_text_color = QColor(self.chart_controller.settings.get_setting("ui", "price_text_color", "#000000"))

        # 创建全局自定义委托
        self.target_line_delegate = TargetLineDelegate(self)

        # 初始化UI
        self._init_ui()

        # 移除此处的信号连接调用，因为在_init_ui中已经调用过
        # self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.main_layout.setSpacing(5)

        # 创建分割器
        self.splitter = QSplitter(Qt.Vertical)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setHandleWidth(8)  # 设置手柄宽度更大，更容易拖动
        self.splitter.setStyleSheet("QSplitter::handle { background-color: #ccc; }")  # 设置手柄样式使其更明显

        # 移除工具栏创建，为图表腾挪更多空间
        # self.toolbar = self._create_toolbar()
        # self.main_layout.addWidget(self.toolbar)

        # 创建表格窗口
        self.table_widget = QTableWidget()
        self.table_widget.setFocusPolicy(Qt.StrongFocus)  # 设置焦点策略以接收键盘事件

        # 设置全局事件过滤器
        self.table_widget.installEventFilter(self)

        # 安装视口事件过滤器 - 用于捕获大小变化事件
        self.table_widget.viewport().installEventFilter(self)

        # 设置选择模式
        self.table_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # 创建自定义委托，用于目标位显示
        self.target_line_delegate = TargetLineDelegate(self)
        self.table_widget.setItemDelegate(self.target_line_delegate)

        # 添加到分割器
        self.splitter.addWidget(self.table_widget)

        # 创建成交量表格
        self.volume_table = QTableWidget()
        self.volume_table.setMinimumHeight(50)  # 设置最小高度而不是最大高度
        # 使用Expanding策略而不是Fixed，允许用户调整大小
        self.volume_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.splitter.addWidget(self.volume_table)

        # 添加分割器到主布局
        self.main_layout.addWidget(self.splitter)

        # 设置初始分割比例 - 主图占75%，成交量占25%
        self.splitter.setSizes([75, 25])

        # 从设置中加载分割器的状态
        splitter_sizes = self.chart_controller.settings.get_setting("ui", "splitter_sizes", None)
        if splitter_sizes and isinstance(splitter_sizes, list) and len(splitter_sizes) == 2:
            # 确保值是整数
            splitter_sizes = [int(size) for size in splitter_sizes]
            self.splitter.setSizes(splitter_sizes)

        # 创建状态栏
        self.status_bar = QLabel("就绪")
        self.status_bar.setFrameShape(QFrame.StyledPanel)
        self.status_bar.setFrameShadow(QFrame.Sunken)
        self.main_layout.addWidget(self.status_bar)

        # 创建设置面板
        self.settings_panel = self._create_settings_panel()
        self.main_layout.addWidget(self.settings_panel)

        # 设置初始大小
        self.resize(800, 600)

        # 设置默认样式
        self._set_table_style()

        # 连接信号和槽
        self._connect_signals()

        # 初始化自动格值计算
        self.auto_box_size = self.chart_controller.settings.get_setting("chart", "auto_box_size", True)
        if self.auto_box_size:
            self.box_size_spin.setValue(0.1)  # 设置为特殊值，显示为空白
            self.box_size_spin.setEnabled(False)
        else:
            self.box_size_spin.setValue(self.box_size)
            self.box_size_spin.setEnabled(True)
        self.auto_box_size_check.setChecked(self.auto_box_size)

        # 设置反转数量
        self.reversal_spin.setValue(self.reversal_amount)

        # 设置方法下拉框
        if self.method == "高低价法":
            self.method_combo.setCurrentIndex(0)
        elif self.method == "收盘价法":
            self.method_combo.setCurrentIndex(1)
        else:
            self.method_combo.setCurrentIndex(0)  # 默认为高低价法

        # 设置成交量单位
        self.volume_scale_spin.setValue(self.volume_scale)

        # 设置成交量显示状态
        self.show_volume_check.setChecked(self.show_volume_chart)

        # 确保表格控件可以获得焦点和接收键盘事件
        self.table_widget.setFocusPolicy(Qt.StrongFocus)  # 强制获得焦点
        self.setFocusProxy(self.table_widget)  # 设置焦点代理
        self.table_widget.installEventFilter(self)  # 安装事件过滤器

        # 安装视口事件过滤器 - 用于捕获大小变化事件
        self.table_widget.viewport().installEventFilter(self)

        self.table_widget.setFocus()  # 初始时设置焦点

        # 初始化完成后，根据设置同步成交量显示状态
        self._sync_volume_display_on_init()

    def _sync_volume_display_on_init(self):
        """初始化时同步成交量显示状态"""
        # 获取分割器中的成交量容器
        volume_container = self.splitter.widget(1)
        
        if self.show_volume_chart:
            # 显示成交量子图
            volume_container.show()
            print(f"[PointFigureChart] 初始化时显示成交量图表: {self.show_volume_chart}")
        else:
            # 隐藏成交量子图
            volume_container.hide()
            print(f"[PointFigureChart] 初始化时隐藏成交量图表: {self.show_volume_chart}")
        
        # 更新界面
        self.splitter.update()

    def _create_settings_panel(self):
        """创建点数图设置面板"""
        # 创建设置面板容器
        settings_panel = QWidget()
        main_settings_layout = QHBoxLayout(settings_panel)
        main_settings_layout.setContentsMargins(2, 2, 2, 2)
        main_settings_layout.setSpacing(5)

        # 创建基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QHBoxLayout(basic_group)
        basic_layout.setContentsMargins(5, 5, 5, 5)
        basic_layout.setSpacing(5)

        # 创建颜色设置组
        color_group = QGroupBox("颜色设置")
        color_layout = QHBoxLayout(color_group)
        color_layout.setContentsMargins(5, 5, 5, 5)
        color_layout.setSpacing(5)

        # 创建成交量设置组
        volume_group = QGroupBox("成交量设置")
        volume_layout = QHBoxLayout(volume_group)
        volume_layout.setContentsMargins(5, 5, 5, 5)
        volume_layout.setSpacing(5)

        # 基本设置布局 - 使用水平布局排列参数，减少垂直空间占用
        params_layout = QGridLayout()
        params_layout.setSpacing(3)

        # 格子大小设置
        box_size_label = QLabel("格值:")
        self.box_size_spin = QDoubleSpinBox()
        self.box_size_spin.setRange(0.1, 1000.0)
        self.box_size_spin.setSingleStep(0.1)
        self.box_size_spin.setValue(self.box_size)
        self.box_size_spin.setDecimals(2)
        self.box_size_spin.setFixedWidth(55)  # 减小宽度
        self.box_size_spin.setSpecialValueText(" ")  # 设置特殊值文本为空白，表示自动计算
        self.box_size_spin.setValue(0.1)  # 设置初始值为特殊值

        # 添加自动格值复选框
        self.auto_box_size_check = QCheckBox("自动")
        self.auto_box_size_check.setChecked(True)
        self.auto_box_size_check.stateChanged.connect(self._toggle_auto_box_size)

        # 创建格值设置的水平布局
        box_size_layout = QHBoxLayout()
        box_size_layout.setSpacing(3)
        box_size_layout.addWidget(box_size_label)
        box_size_layout.addWidget(self.box_size_spin)
        box_size_layout.addWidget(self.auto_box_size_check)

        # 反转数量设置
        reversal_label = QLabel("转向值:")
        self.reversal_spin = QSpinBox()
        self.reversal_spin.setRange(1, 10)
        self.reversal_spin.setValue(self.reversal_amount)
        self.reversal_spin.setFixedWidth(40)  # 减小宽度

        # 方法选择
        method_label = QLabel("绘制:")
        self.method_combo = QComboBox()
        self.method_combo.addItems(["高低价法", "收盘价法"])
        self.method_combo.setCurrentText(self.method)
        self.method_combo.setFixedWidth(70)  # 减小宽度

        # 网格线样式
        grid_style_label = QLabel("网格:")  # 缩短标签文本
        self.grid_style_combo = QComboBox()
        self.grid_style_combo.addItems(["实线", "虚线", "点线"])
        self.grid_style_combo.setCurrentIndex(0)  # 默认实线
        self.grid_style_combo.currentIndexChanged.connect(self._set_grid_style)
        self.grid_style_combo.setFixedWidth(60)  # 减小宽度

        # 添加基本设置到布局
        row = 0
        col = 0
        params_layout.addLayout(box_size_layout, row, col, 1, 2)

        col += 2
        params_layout.addWidget(reversal_label, row, col)
        params_layout.addWidget(self.reversal_spin, row, col+1)

        col += 2
        params_layout.addWidget(method_label, row, col)
        params_layout.addWidget(self.method_combo, row, col+1)

        col += 2
        params_layout.addWidget(grid_style_label, row, col)
        params_layout.addWidget(self.grid_style_combo, row, col+1)

        # 添加参数布局到基本设置中
        basic_layout.addLayout(params_layout)

        # 颜色设置布局 - 使用水平布局排列颜色按钮，减少垂直空间占用
        colors_layout = QGridLayout()
        colors_layout.setSpacing(3)

        # 创建更紧凑的小尺寸颜色按钮
        button_size = 20

        # 背景色
        bg_color_label = QLabel("背景色:")
        self.bg_color_button = QPushButton()
        self.bg_color_button.setFixedSize(button_size, button_size)
        self.bg_color_button.setStyleSheet(f"background-color: {self.background_color.name()}; border: 1px solid #888;")
        self.bg_color_button.clicked.connect(self._set_background_color)

        # 网格线颜色
        grid_color_label = QLabel("网格线:")
        self.grid_color_button = QPushButton()
        self.grid_color_button.setFixedSize(button_size, button_size)
        self.grid_color_button.setStyleSheet(f"background-color: {self.grid_color.name()}; border: 1px solid #888;")
        self.grid_color_button.clicked.connect(self._set_grid_color)

        # X符号颜色
        x_color_label = QLabel("X符号:")
        self.x_color_button = QPushButton()
        self.x_color_button.setFixedSize(button_size, button_size)
        self.x_color_button.setStyleSheet(f"background-color: {self.x_symbol_color.name()}; border: 1px solid #888;")
        self.x_color_button.clicked.connect(self._set_x_color)

        # O符号颜色
        o_color_label = QLabel("O符号:")
        self.o_color_button = QPushButton()
        self.o_color_button.setFixedSize(button_size, button_size)
        self.o_color_button.setStyleSheet(f"background-color: {self.o_symbol_color.name()}; border: 1px solid #888;")
        self.o_color_button.clicked.connect(self._set_o_color)

        # 上涨量颜色
        vol_up_color_label = QLabel("上涨量:")
        self.vol_up_color_button = QPushButton()
        self.vol_up_color_button.setFixedSize(button_size, button_size)
        self.vol_up_color_button.setStyleSheet(f"background-color: {self.volume_up_color.name()}; border: 1px solid #888;")
        self.vol_up_color_button.clicked.connect(self._set_volume_up_color)

        # 下跌量颜色
        vol_down_color_label = QLabel("下跌量:")
        self.vol_down_color_button = QPushButton()
        self.vol_down_color_button.setFixedSize(button_size, button_size)
        self.vol_down_color_button.setStyleSheet(f"background-color: {self.volume_down_color.name()}; border: 1px solid #888;")
        self.vol_down_color_button.clicked.connect(self._set_volume_down_color)

        # 添加颜色设置到布局
        row = 0
        col = 0
        colors_layout.addWidget(bg_color_label, row, col)
        colors_layout.addWidget(self.bg_color_button, row, col+1)

        col += 2
        colors_layout.addWidget(grid_color_label, row, col)
        colors_layout.addWidget(self.grid_color_button, row, col+1)

        col += 2
        colors_layout.addWidget(x_color_label, row, col)
        colors_layout.addWidget(self.x_color_button, row, col+1)

        col += 2
        colors_layout.addWidget(o_color_label, row, col)
        colors_layout.addWidget(self.o_color_button, row, col+1)

        col += 2
        colors_layout.addWidget(vol_up_color_label, row, col)
        colors_layout.addWidget(self.vol_up_color_button, row, col+1)

        col += 2
        colors_layout.addWidget(vol_down_color_label, row, col)
        colors_layout.addWidget(self.vol_down_color_button, row, col+1)

        # 添加颜色布局到颜色设置组
        color_layout.addLayout(colors_layout)

        # 成交量设置布局
        volume_settings_layout = QHBoxLayout()
        volume_settings_layout.setSpacing(5)

        # 成交量显示/隐藏设置
        self.show_volume_check = QCheckBox("显示成交量")
        self.show_volume_check.setChecked(self.show_volume_chart)
        self.show_volume_check.setToolTip("选中显示成交量子图，取消隐藏成交量子图")
        self.show_volume_check.stateChanged.connect(self._toggle_volume_chart)

        # 成交量单位设置
        volume_scale_label = QLabel("成交量单位:")
        self.volume_scale_spin = QDoubleSpinBox()
        self.volume_scale_spin.setRange(1.0, 10000.0)  # 支持更大范围
        self.volume_scale_spin.setSingleStep(10.0)  # 增大步长，便于快速调整
        self.volume_scale_spin.setValue(self.volume_scale)
        self.volume_scale_spin.setDecimals(1)
        self.volume_scale_spin.setSuffix(" 万")
        self.volume_scale_spin.setFixedWidth(80)  # 减小宽度
        self.volume_scale_spin.setToolTip("设置成交量显示单位为'万'，例如：设置为500表示500万成交量会显示为满高度柱状图")

        # 添加成交量设置到布局
        volume_settings_layout.addWidget(self.show_volume_check)
        volume_settings_layout.addSpacing(10)  # 增加一些间距
        volume_settings_layout.addWidget(volume_scale_label)
        volume_settings_layout.addWidget(self.volume_scale_spin)
        volume_settings_layout.addStretch()

        # 添加成交量设置到成交量组
        volume_layout.addLayout(volume_settings_layout)

        # 按钮区域 - 改为网格布局，2行2列
        button_layout = QGridLayout()
        button_layout.setSpacing(3)  # 减小间距

        # 第一行按钮 - 现有的两个按钮
        # 复位按钮
        self.default_settings_button = QPushButton("恢复默认")
        self.default_settings_button.clicked.connect(self._restore_default_settings)
        self.default_settings_button.setFixedWidth(70)  # 减小宽度
        self.default_settings_button.setFixedHeight(25)  # 减小高度
        self.default_settings_button.setFont(QFont("", 8))  # 减小字体

        # 应用按钮
        self.apply_button = QPushButton("应用设置")
        self.apply_button.clicked.connect(self.apply_settings)
        self.apply_button.setFixedWidth(70)  # 减小宽度
        self.apply_button.setFixedHeight(25)  # 减小高度
        self.apply_button.setFont(QFont("", 8))  # 减小字体

        # 第二行按钮 - 新增的两个按钮
        # 存为默认按钮
        self.save_as_default_button = QPushButton("存为默认")
        self.save_as_default_button.clicked.connect(self._save_as_default_settings)
        self.save_as_default_button.setFixedWidth(70)  # 减小宽度
        self.save_as_default_button.setFixedHeight(25)  # 减小高度
        self.save_as_default_button.setFont(QFont("", 8))  # 减小字体

        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self._refresh_chart)
        self.refresh_button.setFixedWidth(70)  # 减小宽度
        self.refresh_button.setFixedHeight(25)  # 减小高度
        self.refresh_button.setFont(QFont("", 8))  # 减小字体

        # 添加按钮到网格布局
        button_layout.addWidget(self.default_settings_button, 0, 0)  # 第一行第一列
        button_layout.addWidget(self.apply_button, 0, 1)  # 第一行第二列
        button_layout.addWidget(self.save_as_default_button, 1, 0)  # 第二行第一列
        button_layout.addWidget(self.refresh_button, 1, 1)  # 第二行第二列

        # 向主设置布局添加各组件
        main_settings_layout.addWidget(basic_group, 3)
        main_settings_layout.addWidget(color_group, 4)
        main_settings_layout.addWidget(volume_group, 2)
        main_settings_layout.addLayout(button_layout, 1)

        return settings_panel

    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 缩放控制
        zoom_label = QLabel("缩放:")
        layout.addWidget(zoom_label)

        # 缩小按钮
        zoom_out_btn = QPushButton("-")
        zoom_out_btn.setMaximumWidth(30)
        zoom_out_btn.clicked.connect(self._zoom_out)
        layout.addWidget(zoom_out_btn)

        # 显示缩放级别
        self.zoom_label = QLabel(f"{self.zoom_level}%")
        self.zoom_label.setAlignment(Qt.AlignCenter)
        self.zoom_label.setMinimumWidth(50)
        layout.addWidget(self.zoom_label)

        # 放大按钮
        zoom_in_btn = QPushButton("+")
        zoom_in_btn.setMaximumWidth(30)
        zoom_in_btn.clicked.connect(self._zoom_in)
        layout.addWidget(zoom_in_btn)

        # 添加分隔符
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # 目标价测算按钮
        target_label = QLabel("目标价:")
        layout.addWidget(target_label)

        # 上行目标按钮
        up_target_btn = QPushButton("上行目标")
        up_target_btn.setToolTip("计算上行目标价位 (快捷键: Ctrl+W)")
        up_target_btn.clicked.connect(self._calculate_upward_target)
        layout.addWidget(up_target_btn)

        # 下行目标按钮
        down_target_btn = QPushButton("下行目标")
        down_target_btn.setToolTip("计算下行目标价位 (快捷键: Ctrl+Z)")
        down_target_btn.clicked.connect(self._calculate_downward_target)
        layout.addWidget(down_target_btn)

        # 清除目标线按钮
        clear_target_btn = QPushButton("清除目标线")
        clear_target_btn.setToolTip("清除所有绘制的目标位线")
        clear_target_btn.clicked.connect(self._explicit_clear_target_overlays)
        layout.addWidget(clear_target_btn)

        # 添加分隔符
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        # 目标价位对话框按钮
        target_dialog_btn = QPushButton("目标价位测算")
        target_dialog_btn.clicked.connect(self.show_target_price_dialog)
        layout.addWidget(target_dialog_btn)

        # 趋势线管理按钮
        trendline_btn = QPushButton("趋势线")
        trendline_btn.clicked.connect(self.manage_trendlines)
        layout.addWidget(trendline_btn)

        # 支撑/阻力位按钮
        sr_btn = QPushButton("支撑/阻力位")
        sr_btn.clicked.connect(self.mark_support_resistance)
        layout.addWidget(sr_btn)

        # 添加分布图按钮
        distribution_btn = QPushButton("分布图")
        distribution_btn.setToolTip("显示所选区域的成交量分布图")
        distribution_btn.clicked.connect(self._on_distribution_button_clicked)
        layout.addWidget(distribution_btn)

        # 弹性空间
        layout.addStretch()

        return toolbar

    def _connect_signals(self):
        """连接信号和槽"""
        # 连接设置面板控件的信号
        self.box_size_spin.valueChanged.connect(self._on_box_size_changed)
        self.reversal_spin.valueChanged.connect(self._update_reversal)
        self.method_combo.currentIndexChanged.connect(self._update_method)
        self.show_volume_check.stateChanged.connect(self._toggle_volume_chart)
        self.volume_scale_spin.valueChanged.connect(self._update_volume_scale)
        self.auto_box_size_check.stateChanged.connect(self._toggle_auto_box_size)

        # 连接分割器大小变化信号
        self.splitter.splitterMoved.connect(self._save_splitter_state)

        # 其他信号连接
        # 连接应用设置按钮
        self.apply_button.clicked.connect(self.apply_settings)

        # 单元格点击事件
        self.table_widget.cellClicked.connect(self._handle_cell_click)

        # 连接选择变化信号
        self.table_widget.itemSelectionChanged.connect(self._handle_selection_changed)

        # 连接滚动信号
        self.volume_table.horizontalScrollBar().valueChanged.connect(self._sync_main_scroll)
        self.table_widget.horizontalScrollBar().valueChanged.connect(self._sync_volume_scroll)

        # 添加表格滚动信号连接，用于更新目标叠加层
        self.table_widget.horizontalScrollBar().valueChanged.connect(self._update_target_overlays_on_scroll)
        self.table_widget.verticalScrollBar().valueChanged.connect(self._update_target_overlays_on_scroll)

        # 重写表格的鼠标滚轮事件
        self.table_widget.wheelEvent = self._table_wheel_event
        self.volume_table.wheelEvent = self._volume_table_wheel_event

        # 移除颜色按钮的信号连接，因为已经在_create_settings_panel中连接过了
        # 表格颜色设置 - 已在_create_settings_panel中连接
        # self.bg_color_button.clicked.connect(self._set_background_color)
        # self.grid_color_button.clicked.connect(self._set_grid_color)
        # self.x_color_button.clicked.connect(self._set_x_color)
        # self.o_color_button.clicked.connect(self._set_o_color)

        # 成交量颜色设置 - 已在_create_settings_panel中连接
        # self.vol_up_color_button.clicked.connect(self._set_volume_up_color)
        # self.vol_down_color_button.clicked.connect(self._set_volume_down_color)

        # 显示/隐藏成交量
        self.show_volume_check.stateChanged.connect(self._toggle_volume_chart)

        # 网格线样式
        self.grid_style_combo.currentIndexChanged.connect(self._set_grid_style)

        # 恢复默认设置
        self.default_settings_button.clicked.connect(self._restore_default_settings)

        # 已经在开头连接过这些信号，这里不需要重复连接
        # self.auto_box_size_check.stateChanged.connect(self._toggle_auto_box_size)
        # self.box_size_spin.valueChanged.connect(self._on_box_size_changed)

    def _update_reversal(self, value):
        """更新反转数量"""
        self.reversal_amount = value
        self.settings["reversal_amount"] = value
        # 不立即更新图表，等待用户点击应用按钮

    def _update_method(self, index):
        """更新绘制方法"""
        method_text = self.method_combo.currentText()
        if method_text == "高低价法":
            self.method = "high_low"
        elif method_text == "收盘价法":
            self.method = "close"
        else:
            self.method = "high_low"  # 默认使用高低价法

        self.settings["method"] = self.method
        # 不立即更新图表，等待用户点击应用按钮

    def _update_volume_scale(self, value):
        """更新成交量单位"""
        self.volume_scale = value
        self.settings["volume_scale"] = value
        # 不立即更新图表，等待用户点击应用按钮

    # 添加表格滚动更新目标叠加层的方法
    def _update_target_overlays_on_scroll(self, value):
        """滚动时更新目标叠加层"""
        # 检查是否有目标叠加层
        if hasattr(self, 'target_overlays') and self.target_overlays:
            # 获取滚动位置
            h_scroll = self.table_widget.horizontalScrollBar().value()
            v_scroll = self.table_widget.verticalScrollBar().value()

            print(f"表格滚动更新: 水平={h_scroll}, 垂直={v_scroll}")

            # 重新绘制所有叠加层
            QTimer.singleShot(50, self._draw_overlays)

            # 特别是重新绘制目标位线
            QTimer.singleShot(100, self._draw_target_position_lines)

    def _sync_volume_scroll(self, value):
        """同步成交量表格的水平滚动"""
        self.volume_table.horizontalScrollBar().setValue(value)

    def _sync_main_scroll(self, value):
        """同步主表格的水平滚动"""
        self.table_widget.horizontalScrollBar().setValue(value)

    def _handle_cell_click(self, row, column):
        """处理单元格点击事件"""
        try:
            # 如果点击的是价格列(第0列)，则选中整行
            if column == 0 and self.table_widget and self.table_widget.columnCount() > 1:
                # 取消当前选择
                self.table_widget.clearSelection()

                # 选择整行 - 从第1列到最后一列
                self.table_widget.setRangeSelected(
                    QTableWidgetSelectionRange(row, 1, row, self.table_widget.columnCount() - 1),
                    True
                )
        except Exception as e:
            print(f"处理单元格点击事件出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _table_wheel_event(self, event):
        """处理滚轮事件，用于缩放和上下滚动"""
        # 检查是否按下Ctrl键
        if event.modifiers() & Qt.ControlModifier:
            # 检查是否禁用了缩放
            if self.zoom_disabled:
                # 显示简短的状态栏提示，不弹出对话框以免打断用户操作
                self.status_bar.setText("存在活跃的目标线，缩放功能暂时禁用")
                # 设置定时器5秒后恢复状态栏文本
                QTimer.singleShot(5000, lambda: self.status_bar.setText("就绪"))
                # 阻止事件继续传播
                event.accept()
                return

            # 获取滚轮角度增量
            delta = event.angleDelta().y()

            if delta > 0:
                # 向上滚动，放大
                self._zoom_in()
            else:
                # 向下滚动，缩小
                self._zoom_out()

            # 停止事件传播
            event.accept()
        else:
            # 不按Ctrl键时实现表格上下滚动
            # 获取滚轮角度增量
            delta = event.angleDelta().y()

            # 获取当前垂直滚动条位置
            scrollbar = self.table_widget.verticalScrollBar()
            current_pos = scrollbar.value()

            # 计算新的滚动位置（滚动速度可调整）
            # 针对不同的显示数据量调整滚动速度
            row_height = self.table_widget.rowHeight(0)
            visible_rows = self.table_widget.viewport().height() / row_height

            # 根据显示行数动态调整滚动速度，行数越多，滚动越快
            scroll_speed = min(max(2, visible_rows / 10), 5)

            # 计算滚动距离，注意delta方向（正值向上，负值向下）
            new_pos = current_pos - delta / 120 * scroll_speed * row_height

            # 确保不超出范围
            new_pos = max(0, min(new_pos, scrollbar.maximum()))

            # 设置新的滚动位置
            scrollbar.setValue(int(new_pos))

            # 停止事件传播
            event.accept()

    def _zoom_in(self):
        """放大点数图"""
        # 检查是否禁用了缩放
        if self.zoom_disabled:
            QMessageBox.information(self, "提示", "存在活跃的目标线，缩放功能暂时禁用\n请先清除目标线后再进行缩放操作")
            return

        # 增加缩放级别
        if self.zoom_level < 200:
            self.zoom_level += 10

            # 保存缩放级别到设置
            self.chart_controller.settings.set_setting("web_view", "zoom_level", self.zoom_level)
            self.chart_controller.settings.save_settings()
            print(f"[PointFigureChart] 放大 - 缩放级别已保存: {self.zoom_level}")

            # 使用定时器防抖
            if not self.zoom_timer.isActive():
                self.zoom_timer.start(self.zoom_debounce)

            # 更新缩放级别标签（如果存在）
            if hasattr(self, 'zoom_label'):
                self.zoom_label.setText(f"{self.zoom_level}%")

    def _zoom_out(self):
        """缩小点数图"""
        # 检查是否禁用了缩放
        if self.zoom_disabled:
            QMessageBox.information(self, "提示", "存在活跃的目标线，缩放功能暂时禁用\n请先清除目标线后再进行缩放操作")
            return

        # 减少缩放级别 - 修改最小限制从50改为20，支持极限缩小
        if self.zoom_level > 20:
            self.zoom_level -= 10

            # 保存缩放级别到设置
            self.chart_controller.settings.set_setting("web_view", "zoom_level", self.zoom_level)
            self.chart_controller.settings.save_settings()
            print(f"[PointFigureChart] 缩小 - 缩放级别已保存: {self.zoom_level}")

            # 使用定时器防抖
            if not self.zoom_timer.isActive():
                self.zoom_timer.start(self.zoom_debounce)

            # 更新缩放级别标签（如果存在）
            if hasattr(self, 'zoom_label'):
                self.zoom_label.setText(f"{self.zoom_level}%")

    def _apply_zoom_debounced(self):
        """防抖处理后应用缩放"""
        self._apply_zoom()

        # 强制更新视图
        self.table_widget.viewport().update()
        self.volume_table.viewport().update()

        # 延迟重绘趋势线和支撑/阻力位
        QTimer.singleShot(50, self._draw_overlays)

        # 重新绘制目标位线，确保它们适应新的缩放级别
        QTimer.singleShot(100, self._draw_target_position_lines)

        # 确保叠加层在绘制后可见
        QTimer.singleShot(200, self._ensure_overlays_visible)
        QTimer.singleShot(500, self._ensure_overlays_visible)
        QTimer.singleShot(1000, self._ensure_overlays_visible)
        QTimer.singleShot(2000, self._ensure_overlays_visible)
        QTimer.singleShot(5000, self._ensure_overlays_visible)
        QTimer.singleShot(10000, self._ensure_overlays_visible)  # 增加更长的延时
        QTimer.singleShot(15000, self._ensure_overlays_visible)  # 增加更长的延时
        QTimer.singleShot(20000, self._ensure_overlays_visible)  # 增加更长的延时
        QTimer.singleShot(30000, self._ensure_overlays_visible)  # 增加更长的延时

    def _apply_zoom(self):
        """应用缩放到表格"""
        if self.chart_data is None or self.chart_data.empty:
                return

        try:
            # 获取价格列表（行）和列数
            prices = self.chart_data.index.tolist()
            num_cols = len(self.chart_data.columns)

            # 设置基础单元格大小 - 修改从30改为15，支持极限缩小
            base_size = 15
            cell_size = int(base_size * self.zoom_level / 100)
            
            # 添加调试输出
            print(f"[_apply_zoom] 应用缩放: 基础大小={base_size}px, 缩放级别={self.zoom_level}%, 最终单元格大小={cell_size}px")

            # 设置点数图表格列宽和行高
            for col in range(1, num_cols + 1):
                self.table_widget.setColumnWidth(col, cell_size)

            for row in range(len(prices)):
                self.table_widget.setRowHeight(row, cell_size)

            # 保存列宽度信息，用于交易量图表对齐
            self.column_widths = [self.table_widget.columnWidth(i) for i in range(num_cols + 1)]

            # 应用相同的列宽到交易量图表
            for col in range(1, num_cols + 1):
                if col < len(self.column_widths):
                    self.volume_table.setColumnWidth(col, self.column_widths[col])

            # 设置符号字体大小
            for row in range(len(prices)):
                for col in range(num_cols):
                    item = self.table_widget.item(row, col + 1)
                    if item and item.text() in ['X', 'O']:
                        font = QFont()
                        font.setBold(True)
                        # 调整字体大小计算，确保在极小缩放下仍然可读
                        font.setPointSize(max(6, int(8 * self.zoom_level / 100)))
                        item.setFont(font)

        except Exception as e:
            print(f"应用缩放出错: {e}")
            logger.error(f"应用缩放出错: {e}")

    def _update_table(self):
        """更新表格"""
        if self.chart_data is None or self.chart_data.empty:
            return

        # 清除表格
        self.table_widget.clear()

        # 检查是否有预扩展的价格范围
        if hasattr(self.chart_data, '_extended_prices') and self.chart_data._extended_prices:
            # 使用预扩展的价格范围
            prices = self.chart_data._extended_prices
            print(f"使用预扩展价格范围: {len(prices)} 个价格点，范围 {min(prices):.2f} - {max(prices):.2f}")
        else:
            # 使用原始价格范围
            prices = self.chart_data.index.tolist()
            print(f"使用原始价格范围: {len(prices)} 个价格点")

        num_cols = len(self.chart_data.columns)

        # 设置表格大小
        self.table_widget.setRowCount(len(prices))
        self.table_widget.setColumnCount(num_cols + 1)  # 额外的列用于显示价格

        # 设置水平标题（列标题）
        for col in range(num_cols):
            self.table_widget.setHorizontalHeaderItem(col + 1, QTableWidgetItem(str(col + 1)))

        # 隐藏垂直标题（行标题）
        self.table_widget.verticalHeader().setVisible(False)

        # 设置价格列标题
        self.table_widget.setHorizontalHeaderItem(0, QTableWidgetItem("价格"))

        # 更新表格样式，确保网格线颜色设置生效
        self.table_widget.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.background_color.name()};
                gridline-color: {self.grid_color.name()};
            }}
        """)

        # 设置网格线样式
        self.table_widget.setShowGrid(True)
        self.table_widget.setGridStyle(self.grid_style)

        # 按格子大小计算对应的价格值
        box_size = self.chart_controller.box_size

        # 填充价格列和数据单元格
        for row, price in enumerate(prices):
            # 创建价格单元格 - 显示实际价格值，根据价格本身和格值确定小数位数
            # 确保显示的小数位数能够正确表示格值的精度
            
            # 检查价格是否为整数
            if price == int(price):
                # 如果价格是整数，检查格值是否为整数
                if box_size == int(box_size):
                    price_text = f"{price:.0f}"
                else:
                    # 格值是小数，即使价格是整数也要显示小数位以保持一致性
                    if box_size >= 0.1:
                        price_text = f"{price:.1f}"
                    elif box_size >= 0.01:
                        price_text = f"{price:.2f}"
                    else:
                        price_text = f"{price:.3f}"
            else:
                # 价格是小数，根据格值精度确定显示位数
                if box_size >= 0.1:
                    price_text = f"{price:.1f}"
                elif box_size >= 0.01:
                    price_text = f"{price:.2f}"
                else:
                    price_text = f"{price:.3f}"

            price_item = QTableWidgetItem(price_text)
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            price_item.setFlags(price_item.flags() & ~Qt.ItemIsSelectable)  # 禁止选择价格列

            # 设置价格列样式
            price_item.setBackground(QBrush(self.price_bg_color))
            price_item.setForeground(QBrush(self.price_text_color))

            self.table_widget.setItem(row, 0, price_item)

            # 为每一列填充单元格
            for col in range(num_cols):
                # 检查当前价格是否在原始数据中存在
                value = None
                if price in self.chart_data.index:
                    # 获取该单元格的值
                    original_row_index = self.chart_data.index.get_loc(price)
                    value = self.chart_data.iloc[original_row_index, col]

                if pd.notna(value):
                    # 创建符号单元格
                    symbol_item = QTableWidgetItem()
                    symbol_item.setTextAlignment(Qt.AlignCenter)

                    # 设置单元格背景色为背景色
                    symbol_item.setBackground(QBrush(self.background_color))

                    # 根据符号类型设置显示
                    if value == 'X':
                        symbol_item.setText("X")
                        symbol_item.setForeground(QBrush(self.x_symbol_color))
                        font = QFont()
                        font.setBold(True)
                        # 调整字体大小计算，确保在极小缩放下仍然可读
                        font.setPointSize(max(6, int(8 * self.zoom_level / 100)))
                        symbol_item.setFont(font)
                    elif value == 'O':
                        symbol_item.setText("O")
                        symbol_item.setForeground(QBrush(self.o_symbol_color))
                        font = QFont()
                        font.setBold(True)
                        # 调整字体大小计算，确保在极小缩放下仍然可读
                        font.setPointSize(max(6, int(8 * self.zoom_level / 100)))
                        symbol_item.setFont(font)

                    self.table_widget.setItem(row, col + 1, symbol_item)
                else:
                    # 创建空单元格
                    empty_item = QTableWidgetItem("")
                    empty_item.setBackground(QBrush(self.background_color))
                    self.table_widget.setItem(row, col + 1, empty_item)

        # 设置列宽和行高 - 根据格值调整价格列宽度
        if box_size >= 1:
            price_col_width = 60  # 整数价格，较窄
        elif box_size >= 0.1:
            price_col_width = 70  # 1位小数，稍宽
        elif box_size >= 0.01:
            price_col_width = 80  # 2位小数，更宽
        else:
            price_col_width = 90  # 3位小数，最宽

        self.table_widget.setColumnWidth(0, price_col_width)

        # 将其余列设置为正方形 - 修改基础大小从30改为15，支持极限缩小
        base_size = 15
        cell_size = int(base_size * self.zoom_level / 100)
        self.cell_size = cell_size  # 将cell_size保存为类的属性

        for col in range(1, num_cols + 1):
            self.table_widget.setColumnWidth(col, cell_size)

        for row in range(len(prices)):
            self.table_widget.setRowHeight(row, cell_size)

        # 固定价格列
        self.table_widget.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)

        # 应用当前缩放级别
        # 添加调试输出，显示当前缩放状态
        print(f"[_update_table] 当前缩放级别: {self.zoom_level}%, 计算的单元格大小: {cell_size}px")
        
        # 滚动到顶部
        self.table_widget.scrollToTop()

        # 保存列宽度信息，用于交易量图表对齐
        self.column_widths = [self.table_widget.columnWidth(i) for i in range(num_cols + 1)]

        # 同步成交量表格的列宽
        if hasattr(self, 'volume_table') and self.volume_table is not None:
            # 设置第一列宽度，与价格列保持一致
            self.volume_table.setColumnWidth(0, price_col_width)

            # 设置数据列宽度
            for col in range(1, num_cols + 1):
                if col < len(self.column_widths):
                    self.volume_table.setColumnWidth(col, self.column_widths[col])

        # 设置选择模式
        self.table_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectItems)

        # 强制更新
        self.table_widget.viewport().update()
        
        # 确保缩放状态正确保存 - 更新表格后重新确认缩放设置
        if hasattr(self, 'zoom_level') and self.zoom_level != 100:
            print(f"[_update_table] 非默认缩放级别({self.zoom_level}%)，确保缩放状态保持")
            # 延迟应用缩放，避免在表格更新过程中发生冲突
            QTimer.singleShot(50, self._apply_zoom_debounced)

        # 延迟绘制趋势线和支撑/阻力位
        QTimer.singleShot(100, self._draw_overlays)

    def _update_volume_chart(self, data):
        """更新交易量图表"""
        if data is None or len(data) == 0 or self.chart_data is None or self.chart_data.empty:
            return

        try:
            # 清除交易量表格
            self.volume_table.clear()

            # 获取列数
            num_cols = len(self.chart_data.columns)

            # 设置交易量表格大小
            self.volume_table.setRowCount(1)  # 只有一行，用于显示交易量
            self.volume_table.setColumnCount(num_cols + 1)  # 额外的列用于标题

            # 设置水平标题（列标题）
            for col in range(num_cols):
                self.volume_table.setHorizontalHeaderItem(col + 1, QTableWidgetItem(str(col + 1)))

            # 隐藏垂直标题（行标题）
            self.volume_table.verticalHeader().setVisible(False)

            # 设置第一列标题
            self.volume_table.setHorizontalHeaderItem(0, QTableWidgetItem("成交量"))

            # 更新交易量表格样式
            self.volume_table.setStyleSheet(f"""
                QTableWidget {{
                    background-color: {self.background_color.name()};
                    gridline-color: {self.grid_color.name()};
                }}
            """)

            # 设置网格线样式
            self.volume_table.setShowGrid(True)
            self.volume_table.setGridStyle(self.grid_style)

            # 设置第一列宽度，根据格值调整
            box_size = self.chart_controller.box_size
            if box_size >= 1:
                price_col_width = 60  # 整数价格，较窄
            elif box_size >= 0.1:
                price_col_width = 70  # 1位小数，稍宽
            elif box_size >= 0.01:
                price_col_width = 80  # 2位小数，更宽
            else:
                price_col_width = 90  # 3位小数，最宽
            self.volume_table.setColumnWidth(0, price_col_width)

            # 将其余列宽度与点数图对齐
            for col in range(1, num_cols + 1):
                if col < len(self.column_widths):
                    self.volume_table.setColumnWidth(col, self.column_widths[col])

            # 创建第一列的标签
            label_item = QTableWidgetItem("量")
            label_item.setTextAlignment(Qt.AlignCenter)
            label_item.setBackground(QBrush(self.price_bg_color))
            label_item.setForeground(QBrush(self.price_text_color))
            self.volume_table.setItem(0, 0, label_item)

            # 计算每列的成交量总和 - 从chart_data的attrs属性中获取volume_info
            column_volumes = []

            if hasattr(self.chart_data, 'attrs') and 'volume_info' in self.chart_data.attrs:
                volume_df = self.chart_data.attrs['volume_info']

                # 计算每列的成交量总和
                for col in range(num_cols):
                    if col in volume_df.columns:
                        # 获取该列所有非空成交量值并求和
                        col_volumes = volume_df[col].dropna().values
                        total_volume = sum(float(v) for v in col_volumes if pd.notna(v) and v != 0)
                        column_volumes.append(total_volume)
                    else:
                        column_volumes.append(0)
            else:
                # 如果没有获取到volume_info，则使用原始数据中的成交量（回退机制）
                if len(data) >= num_cols and 'volume' in data.columns:
                    column_volumes = data['volume'].tolist()[:num_cols]
                    # 如果原始数据列数不够，补充0
                    column_volumes.extend([0] * (num_cols - len(column_volumes)))
                else:
                    column_volumes = [0] * num_cols

            # 找出所有非0成交量的最大值，用于计算比例
            max_volume = max(column_volumes) if column_volumes and max(column_volumes) > 0 else 1

            # 定义基准单位值（作为标准高度的参考值）
            # 可以使用最大成交量或自定义的基准
            base_unit = max(max_volume, self.volume_scale * 10000)  # 以"万"为单位

            # 将成交量数据映射到点数图列
            for col in range(num_cols):
                if col < len(column_volumes):
                    # 创建成交量单元格
                    volume_item = QTableWidgetItem()
                    volume_item.setTextAlignment(Qt.AlignCenter)

                    # 获取该列的成交量总和
                    volume_value = column_volumes[col]

                    # 计算相对于基准单位的比例
                    volume_ratio = min(volume_value / base_unit, 1.0) if base_unit > 0 else 0

                    # 获取当前列的最后一个符号，确定是上涨量还是下跌量
                    last_symbol = None
                    if col in self.chart_data.columns:
                        col_data = self.chart_data[col].dropna()
                        if not col_data.empty:
                            last_symbol = col_data.iloc[-1]  # 获取最后一个非空值

                    # 根据最后符号设置颜色
                    if last_symbol == 'X':
                        volume_color = self.volume_up_color
                    elif last_symbol == 'O':
                        volume_color = self.volume_down_color
                    else:
                        # 如果无法确定，默认为上涨量颜色
                        volume_color = self.volume_up_color

                    # 调整透明度以表示成交量大小
                    adjusted_color = QColor(volume_color)
                    adjusted_color.setAlpha(int(100 + 155 * volume_ratio))  # 确保小成交量也有一定可见度

                    # 设置优化后的柱状图显示方式
                    volume_item.setData(Qt.DecorationRole, self._create_volume_bar(volume_ratio, adjusted_color))

                    # 格式化成交量数值显示
                    if volume_value >= 100000000:  # 亿
                        volume_text = f"{volume_value/100000000:.2f}亿"
                    elif volume_value >= 10000:  # 万
                        volume_text = f"{volume_value/10000:.2f}万"
                    elif volume_value >= 1000:
                        volume_text = f"{volume_value/1000:.1f}千"
                    else:
                        volume_text = str(int(volume_value))

                    # 设置工具提示，增加显示基准单位信息
                    volume_item.setToolTip(f"成交量: {volume_text}\n(实际每格成交量总和)")

                    self.volume_table.setItem(0, col + 1, volume_item)

            # 设置行高 - 增加成交量图表的最小高度
            base_size = 120  # 提高交易量行高，给予更多的显示空间
            self.volume_table.setRowHeight(0, base_size)

            # 固定交易量标签列
            self.volume_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)

            # 强制更新
            self.volume_table.viewport().update()

        except Exception as e:
            print(f"更新交易量图表出错: {e}")
            import traceback
            traceback.print_exc()

    def _create_volume_bar(self, ratio, color):
        """创建成交量柱状图"""
        # 创建一个大小合适的图像
        image_width = self.cell_size - 2  # 稍小于单元格宽度
        image_height = 110  # 增加柱状图高度，与表格行高保持一致

        image = QPixmap(image_width, image_height)
        image.fill(Qt.transparent)  # 设置背景透明

        # 计算柱子高度，确保即使比例很小也有最小高度
        min_height = 2  # 最小高度，确保即使很小的成交量也可见
        bar_height = max(int(image_height * ratio), min_height)

        # 创建QPainter
        painter = QPainter(image)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制柱子 - 从底部向上
        rect = QRect(0, image_height - bar_height, image_width, bar_height)
        painter.fillRect(rect, color)

        # 绘制边框
        border_pen = QPen(QColor(color.darker(120)))
        border_pen.setWidth(1)
        painter.setPen(border_pen)
        painter.drawRect(rect)

        painter.end()

        return image

    def _show_context_menu(self, pos):
        """显示上下文菜单"""
        # 创建右键菜单
        menu = QMenu(self)

        # 添加菜单项
        add_target_action = menu.addAction("添加目标位")
        add_target_action.triggered.connect(self.show_target_price_dialog)

        add_trendline_action = menu.addAction("添加趋势线")
        add_trendline_action.triggered.connect(self.add_trendline)

        mark_support_resistance_action = menu.addAction("标记支撑/阻力位")
        mark_support_resistance_action.triggered.connect(self.mark_support_resistance)

        # 根据所选区域添加相关操作
        selected_ranges = self.table_widget.selectedRanges()
        if selected_ranges:
            menu.addSeparator()
            target_action = menu.addAction("计算目标价位")
            target_action.triggered.connect(self._calculate_target_from_selection)

            # 添加成交量分布图选项 - 使用弹窗方式显示
            volume_dist_action = menu.addAction("显示成交量分布图")
            volume_dist_action.triggered.connect(self._show_volume_distribution_dialog)

        # 添加成交量子图管理菜单
        menu.addSeparator()
        volume_menu = menu.addMenu("成交量子图")

        # 添加成交量子图显示/隐藏选项
        toggle_volume_action = volume_menu.addAction("显示/隐藏成交量子图")
        toggle_volume_action.triggered.connect(lambda: self.show_volume_check.setChecked(not self.show_volume_check.isChecked()))

        # 添加重置成交量子图比例选项
        if self.show_volume_chart:
            reset_splitter_action = volume_menu.addAction("重置成交量子图比例")
            reset_splitter_action.triggered.connect(self._reset_splitter)

        # 添加间隔线
        menu.addSeparator()

        # 添加生成报告选项
        generate_report_action = menu.addAction("生成分析报告")
        generate_report_action.triggered.connect(self.generate_analysis_report)

        # 添加复制图表选项
        copy_chart_action = menu.addAction("复制图表")
        copy_chart_action.triggered.connect(self.copy_chart_to_clipboard)

        # 添加导出图表选项
        export_chart_action = menu.addAction("导出图表")
        export_chart_action.triggered.connect(self.export_chart)

        # 显示菜单
        menu.exec_(self.mapToGlobal(pos))

    def _calculate_target_from_selection(self):
        """从选择区域计算目标价格"""
        if not hasattr(self, 'selected_range'):
            QMessageBox.warning(self, "警告", "请先选择连续的X或O区域")
            return

        # 验证选择是否在同一行且至少3格
        row = self.selected_range['row']
        start_col = self.selected_range['start_col']
        end_col = self.selected_range['end_col']

        # 检查是否为同一行且至少3格
        if end_col - start_col + 1 < 3:
            QMessageBox.warning(self, "警告", "请至少选择同一行连续的3个格子")
            return

        # 获取选择区域的符号类型
        selected_type = None

        # 遍历选择区域，检查符号类型
        for col in range(start_col, end_col + 1):
            item = self.table_widget.item(row, col)
            if item and item.text() in ['X', 'O']:
                # 确定选择区域的符号类型
                if selected_type is None:
                    selected_type = item.text()
                # 如果发现不同类型的符号，给出警告
                elif selected_type != item.text():
                    QMessageBox.warning(self, "警告", "请选择同一类型（X或O）的连续区域")
                    return

        # 如果没有找到有效符号，给出警告
        if selected_type is None:
            QMessageBox.warning(self, "警告", "所选区域没有包含有效的X或O符号")
            return

        # 继续执行原有逻辑...
        # 获取选择范围
        selected_ranges = self.table_widget.selectedRanges()
        if not selected_ranges:
            return

        # 获取第一个选择范围
        selected_range = selected_ranges[0]

        # 获取列范围
        start_col = selected_range.leftColumn()
        end_col = selected_range.rightColumn()

        # 调整为数据列索引（跳过价格列）
        start_col -= 1
        end_col -= 1

        # 创建并显示目标价位对话框
        dialog = TargetPriceDialog(self.chart_data, self.chart_controller.box_size, self.chart_controller.reversal_amount, self)

        # 设置起始和结束位置
        dialog.start_pos_spin.setValue(start_col + 1)
        dialog.end_pos_spin.setValue(end_col + 1)

        # 执行计算
        dialog._calculate_target()

        # 显示对话框
        dialog.exec_()

    def show_target_price_dialog(self):
        """显示目标价位测算对话框"""
        # 检查是否有图表数据
        if self.chart_data is None:
            QMessageBox.warning(self, "警告", "请先生成点数图")
            return

        # 创建目标价位测算对话框
        dialog = TargetPriceDialog(self.chart_data, self.chart_controller.box_size, self.chart_controller.reversal_amount, self)

        # 显示对话框
        dialog.exec_()

    def apply_settings(self):
        """应用设置"""
        try:
            # 检查用户是否手动修改了格值（输入框不为空）
            user_modified_box_size = not self.auto_box_size_check.isChecked() or self.box_size_spin.value() != 0.1

            # 获取设置
            if user_modified_box_size:
                # 关闭自动格值计算，使用用户输入的值
                self.auto_box_size = False
                self.auto_box_size_check.setChecked(False)
                self.box_size_spin.setEnabled(True)
                box_size = self.box_size_spin.value()
            else:
                # 使用自动计算的格值
                box_size = self.box_size

            reversal = self.reversal_spin.value()

            # 获取方法
            method_text = self.method_combo.currentText()
            if method_text == "高低价法":
                method = "high_low"
            elif method_text == "收盘价法":
                method = "close"
            else:
                method = "high_low"  # 默认使用高低价法

            # 获取成交量单位
            volume_scale = self.volume_scale_spin.value()

            # 获取成交量显示状态
            show_volume = self.show_volume_check.isChecked()

            # 更新设置
            self.box_size = box_size
            self.reversal_amount = reversal
            self.method = method
            self.volume_scale = volume_scale
            self.show_volume_chart = show_volume

            # 获取额外列数（从设置对话框）
            try:
                extra_columns = self.settings.get("extra_columns", 3)
            except:
                extra_columns = 3

            # 更新图表控制器
            self.chart_controller.set_chart_parameters(box_size, reversal, method, extra_columns)

            # 保存设置到设置字典
            # 更新图表参数相关设置
            self.settings["box_size"] = box_size
            self.settings["reversal_amount"] = reversal
            self.settings["method"] = method

            # 更新UI颜色相关设置
            self.settings["background_color"] = self.background_color.name()
            self.settings["grid_color"] = self.grid_color.name()
            self.settings["x_color"] = self.x_symbol_color.name()
            self.settings["o_color"] = self.o_symbol_color.name()
            self.settings["volume_up_color"] = self.volume_up_color.name()
            self.settings["volume_down_color"] = self.volume_down_color.name()

            # 更新成交量相关设置
            self.settings["volume_scale"] = volume_scale
            self.settings["show_volume"] = show_volume

            # 保存当前分割器状态
            if self.show_volume_chart:
                self.settings["splitter_sizes"] = self.splitter.sizes()

            # 重新生成点数图
            self.update_chart()

            # 更新状态栏
            if hasattr(self.parent(), 'statusBar'):
                self.parent().statusBar().showMessage("设置已应用", 3000)

            return True
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用设置时出错: {str(e)}")
            return False

    def update_settings(self):
        """从控制器加载最新设置并更新图表"""
        try:
            # 从控制器获取最新设置
            self.box_size = self.chart_controller.box_size
            self.reversal_amount = self.chart_controller.reversal_amount
            self.method = self.chart_controller.method

            # 更新UI控件以匹配新设置
            # 格值设置
            if not self.auto_box_size:
                self.box_size_spin.setValue(self.box_size)

            # 反转数量
            self.reversal_spin.setValue(self.reversal_amount)

            # 绘制方法
            method_index = 0  # 默认为高低价法
            if self.method == "close":
                method_index = 1
            self.method_combo.setCurrentIndex(method_index)

            # 更新Web视图缩放级别
            self.zoom_level = self.chart_controller.settings.get_setting("web_view", "zoom_level", 100)
            if hasattr(self, 'zoom_label'):
                self.zoom_label.setText(f"{self.zoom_level}%")

            # 更新成交量显示设置
            self.show_volume_chart = self.chart_controller.settings.get_setting("ui", "show_volume_chart", True)
            if hasattr(self, 'volume_checkbox'):
                self.volume_checkbox.setChecked(self.show_volume_chart)
            
            # 应用成交量显示设置
            if hasattr(self, 'splitter') and self.splitter.count() > 1:
                volume_container = self.splitter.widget(1)
                if self.show_volume_chart:
                    volume_container.show()
                else:
                    volume_container.hide()

            # 更新图表
            self.update_chart()

            # 显示成功消息
            if hasattr(self.parent(), 'statusBar'):
                self.parent().statusBar().showMessage("设置已更新", 3000)

            return True
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新设置时出错: {str(e)}")
            return False

    def add_trendline(self):
        """添加趋势线"""
        # 检查是否有图表数据
        if self.chart_data is None or self.chart_data.empty:
            QMessageBox.warning(self, "警告", "请先导入数据并生成点数图")
            return

        # 显示趋势线对话框
        dialog = TrendlineDialog(self)

        # 如果用户点击了确定按钮
        if dialog.exec_():
            # 获取趋势线数据
            trendline_data = dialog.get_trendline_data()

            # 添加到趋势线列表
            self.trendlines.append(trendline_data)

            # 使用定时器确保在UI更新后绘制
            QTimer.singleShot(50, self._draw_overlays)

            # 显示成功消息
            QMessageBox.information(self, "添加成功", f"已添加趋势线：{trendline_data['name']}")

    def manage_trendlines(self):
        """管理趋势线"""
        # 检查是否有趋势线
        if not self.trendlines:
            QMessageBox.information(self, "提示", "当前没有趋势线，请先添加趋势线")
            return

        # 显示趋势线管理对话框
        dialog = TrendlineManagerDialog(self.trendlines, self)

        # 如果用户点击了确定按钮
        if dialog.exec_():
            # 获取更新后的趋势线列表
            self.trendlines = dialog.get_trendlines()

            # 重新绘制叠加元素
            self._draw_overlays()

    def mark_support_resistance(self):
        """标记支撑/阻力位"""
        # 检查是否有图表数据
        if self.chart_data is None or self.chart_data.empty:
            QMessageBox.warning(self, "警告", "请先导入数据并生成点数图")
            return

        # 显示支撑/阻力位对话框
        dialog = SupportResistanceDialog(self.chart_data, self.chart_controller.box_size, self)

        # 如果用户点击了确定按钮
        if dialog.exec_():
            # 获取选择的支撑/阻力位
            selected_levels = dialog.get_selected_levels()

            # 更新支撑/阻力位列表
            self.support_resistance_levels = selected_levels

            # 使用定时器确保在UI更新后绘制
            QTimer.singleShot(50, self._draw_overlays)

            # 显示成功信息
            QMessageBox.information(self, "标记成功", f"已标记 {len(self.support_resistance_levels)} 个支撑/阻力位")

    def update_chart(self):
        """更新图表"""
        # 获取数据
        data = self.chart_controller.data_controller.get_data()
        if data is None or len(data) == 0:
            return

        # 如果启用了自动格值计算，根据价格计算推荐格值
        if self.auto_box_size:
            # 取最新价格（最后一个收盘价）作为参考价格
            latest_price = data['close'].iloc[-1]
            self.box_size = self._calculate_box_size(latest_price)
            # 更新控制器的格值
            self.chart_controller.box_size = self.box_size
            # 保持格值设置框为空白
            self.box_size_spin.setValue(0.1)  # 保持特殊值，显示为空白

        # 创建并显示进度对话框
        from PyQt5.QtWidgets import QProgressDialog
        from PyQt5.QtCore import Qt, QTimer

        progress = QProgressDialog("正在生成点数图表...", "取消", 0, 100, self)
        progress.setWindowTitle("处理中")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)  # 立即显示
        progress.setAutoClose(True)
        progress.setValue(0)

        # 连接进度信号
        self.chart_controller.chart_progress.connect(progress.setValue)

        # 连接取消信号
        canceled = [False]  # 使用列表作为可变引用
        def on_canceled():
            canceled[0] = True
            progress.setLabelText("正在取消操作...")

        progress.canceled.connect(on_canceled)

        # 显示进度对话框
        progress.show()

        # 使用单次计时器确保进度对话框显示后再开始生成图表
        def generate_chart():
            # 生成点数图数据
            self.chart_data = self.chart_controller.generate_point_figure_chart(data)

            # 如果成功生成图表，更新UI
            if self.chart_data is not None and not canceled[0]:
                # 获取价格范围并创建价格标签
                self._update_table()

                # 获取成交量数据进行处理
                if 'volume' in data.columns:
                    # 转换成交量数据适配点数图列
                    self._update_volume_chart(data)

                # 更新状态信息
                self._update_status_info(data)

            # 禁用未导入数据的功能按钮
            has_data = self.chart_data is not None and not self.chart_data.empty

            # 更新标题为价格范围和格值
            if has_data:
                # 如果启用了自动格值，显示计算的格值，否则显示用户设置的格值
                if self.auto_box_size:
                    box_size_str = f"{self.box_size} (自动)"
                else:
                    box_size_str = f"{self.box_size}"

                self.status_bar.setText(f"收盘价价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}，格值: {box_size_str}")

            # 关闭进度对话框
            progress.close()

        # 确保UI元素更新后再开始生成图表
        QTimer.singleShot(100, generate_chart)

    def _update_status_info(self, data):
        """更新状态栏信息"""
        if data is None or len(data) == 0:
            return

        try:
            # 获取股票代码和市场类型信息
            stock_code = "未知代码"
            market_type = "未知市场"
            data_source = "本地文件"

            # 调试信息
            print(f"更新状态栏: 数据属性: {type(data)}, 列名: {data.columns.tolist()}")

            # 检查data.name是否是Series（从通达信导入的数据可能会出现这种情况）
            name_value = None
            if hasattr(data, 'name'):
                if isinstance(data.name, pd.Series):
                    # 如果是Series，取第一个值
                    if not data.name.empty:
                        name_value = data.name.iloc[0]
                        print(f"name是Series，第一个值: {name_value}")
                else:
                    # 正常情况，直接使用name属性
                    name_value = data.name
                    print(f"name是属性: {name_value}")

            # 从数据框的name属性获取信息
            if name_value:
                data_name = str(name_value)
                print(f"处理数据名称: {data_name}")

                # 分析name属性（格式可能是：MARKET.CODE (周期) - 数据源）
                if ' - ' in data_name:
                    name_parts = data_name.split(' - ')
                    code_part = name_parts[0].split(' ')[0]  # 获取第一部分的第一个词

                    if '.' in code_part:
                        parts = code_part.split('.')
                        if len(parts) == 2:
                            market_type, stock_code = parts

                    # 提取数据源
                    if len(name_parts) > 1:
                        data_source = name_parts[1]

                # 处理交易代码(300681)格式
                elif '(' in data_name and ')' in data_name:
                    import re
                    code_match = re.search(r'\((\d{6})\)', data_name)
                    if code_match:
                        stock_code = code_match.group(1)
                        # 根据股票代码推断市场
                        if stock_code.startswith('6'):
                            market_type = 'SH'
                        elif stock_code.startswith('0') or stock_code.startswith('3'):
                            market_type = 'SZ'

            # 从数据列中获取信息（如果name属性中没有）
            if stock_code == "未知代码" and 'code' in data.columns:
                # 安全获取第一个值
                code_value = data['code'].iloc[0] if not data['code'].empty else None
                if code_value is not None:
                    stock_code = str(code_value)

            if market_type == "未知市场" and 'market' in data.columns:
                # 安全获取第一个值
                market_value = data['market'].iloc[0] if not data['market'].empty else None
                if market_value is not None:
                    market_type = str(market_value)

            # 如果仍然未知但文件名中有代码，尝试从文件名提取
            if stock_code == "未知代码" and hasattr(self.chart_controller, 'file_path') and self.chart_controller.file_path:
                import re
                import os
                file_name = os.path.basename(self.chart_controller.file_path)
                code_match = re.search(r'(\d{6})', file_name)
                if code_match:
                    stock_code = code_match.group(1)
                    # 根据股票代码推断市场
                    if stock_code.startswith('6'):
                        market_type = 'SH'
                    elif stock_code.startswith('0') or stock_code.startswith('3'):
                        market_type = 'SZ'

            # 获取日期范围（增加对date列不存在的处理）
            date_range = "无日期信息"
            if 'date' in data.columns and not data['date'].empty:
                start_date = data['date'].iloc[0]
                end_date = data['date'].iloc[-1]
                date_range = f"{start_date}~{end_date}"

            # 获取数据总数
            data_count = len(data)

            # 获取最高价和最低价（确保这些列存在）
            max_price = data['high'].max() if 'high' in data.columns and not data['high'].empty else 0
            min_price = data['low'].min() if 'low' in data.columns and not data['low'].empty else 0
            latest_price = data['close'].iloc[-1] if 'close' in data.columns and not data['close'].empty else 0

            # 获取点数图参数
            box_size = self.chart_controller.box_size
            reversal_amount = self.chart_controller.reversal_amount

            # 获取绘制方法
            method = self.chart_controller.method
            method_text = ""
            if method == "high_low":
                method_text = "高低价法"
            elif method == "close":
                method_text = "收盘价法"
            else:
                method_text = "高低价法"  # 默认使用高低价法

            # 构建状态栏信息，在开头添加股票代码和市场类型，用分隔符隔开
            # 格式：股票代码(市场) - 数据源 | 日期范围 数据总数 ... 其他信息
            status_text = f"{market_type}.{stock_code} - {data_source} | {date_range} 数据总数: {data_count} 最高价: {max_price:.2f} 最低价: {min_price:.2f} 最新价: {latest_price:.2f} 格值: {box_size} 转向格: {reversal_amount} 绘制方法: {method_text}"

            # 打印状态栏信息到控制台，便于验证
            print(f"状态栏信息: {status_text}")

            # 更新主窗口状态栏
            main_window = self.window()
            if hasattr(main_window, 'statusBar'):
                main_window.statusBar().showMessage(status_text)

                # 同时更新主窗口的导入状态标签
                if hasattr(main_window, 'import_status_label'):
                    main_window.import_status_label.setText("数据导入成功")

        except Exception as e:
            import traceback
            print(f"更新状态栏信息出错: {e}")
            print(traceback.format_exc())
            # 出错时设置一个简单的状态信息
            try:
                main_window = self.window()
                if hasattr(main_window, 'statusBar'):
                    main_window.statusBar().showMessage(f"数据已导入，共 {len(data) if data is not None else 0} 条记录")
            except:
                pass

    def _draw_overlays(self):
        """绘制所有叠加元素（趋势线和支撑/阻力位）"""
        # 确保有数据可绘制
        if self.chart_data is None or self.chart_data.empty:
            return

        # 先清除之前的绘制内容，强制刷新视图
        self.table_widget.viewport().update()

        # 确保表格数据更新完成后再开始绘制
        # 显示目标位线（直接调用而不是使用定时器）
        if hasattr(self, 'target_lines') and self.target_lines:
            self._draw_target_position_lines()

        # 延迟绘制其他元素，以确保目标位线显示
        QTimer.singleShot(200, self._draw_support_resistance_levels)
        QTimer.singleShot(250, self._draw_trendlines)

        # 最后再次强制刷新确保绘制内容可见
        QTimer.singleShot(350, lambda: self.table_widget.viewport().update())

    def _draw_trendlines(self):
        """绘制趋势线"""
        if not hasattr(self, 'trendlines') or not self.trendlines:
            return

        try:
            # 获取绘图区域
            viewport = self.table_widget.viewport()
            view_rect = viewport.rect()

            # 创建QPainter对象
            painter = QPainter(viewport)
            painter.setRenderHint(QPainter.Antialiasing, True)

            for trendline in self.trendlines:
                try:
                    # 检查趋势线是否有效
                    if 'start_column' not in trendline or 'start_price' not in trendline or 'end_column' not in trendline or 'end_price' not in trendline:
                        continue

                    # 获取线条颜色
                    color = trendline.get('color', Qt.black)
                    if isinstance(color, str):
                        color = QColor(color)

                    # 设置画笔
                    pen = QPen(color)
                    pen.setWidth(trendline.get('width', 2))

                    # 设置线条样式
                    line_style = trendline.get('style', 'solid')
                    if line_style == 'dashed':
                        pen.setStyle(Qt.DashLine)
                    elif line_style == 'dotted':
                        pen.setStyle(Qt.DotLine)
                    elif line_style == 'dash_dot':
                        pen.setStyle(Qt.DashDotLine)
                    else:
                        pen.setStyle(Qt.SolidLine)

                    painter.setPen(pen)

                    # 查找价格对应的行索引
                    start_row = -1
                    end_row = -1

                    # 找到最接近的价格行
                    for i in range(self.table_widget.rowCount()):
                        price_text = self.table_widget.verticalHeaderItem(i).text()
                        price = float(price_text) if price_text else 0

                        if start_row == -1 and abs(price - trendline['start_price']) < self.box_size / 2:
                            start_row = i

                        if end_row == -1 and abs(price - trendline['end_price']) < self.box_size / 2:
                            end_row = i

                        if start_row != -1 and end_row != -1:
                            break

                    # 如果找不到精确匹配，使用最近的价格
                    if start_row == -1:
                        min_diff = float('inf')
                        for i in range(self.table_widget.rowCount()):
                            price_text = self.table_widget.verticalHeaderItem(i).text()
                            if not price_text:
                                continue
                            price = float(price_text)
                            diff = abs(price - trendline['start_price'])
                            if diff < min_diff:
                                min_diff = diff
                                start_row = i

                    if end_row == -1:
                        min_diff = float('inf')
                        for i in range(self.table_widget.rowCount()):
                            price_text = self.table_widget.verticalHeaderItem(i).text()
                            if not price_text:
                                continue
                            price = float(price_text)
                            diff = abs(price - trendline['end_price'])
                            if diff < min_diff:
                                min_diff = diff
                                end_row = i

                    # 获取列索引
                    start_col = trendline['start_column'] - 1
                    end_col = trendline['end_column'] - 1

                    # 如果超出范围，调整为可见范围
                    visible_columns = self.table_widget.horizontalHeader().count()
                    if start_col >= visible_columns:
                        start_col = visible_columns - 1
                    if end_col >= visible_columns:
                        end_col = visible_columns - 1

                    # 获取单元格中心点坐标
                    start_rect = self.table_widget.visualRect(self.table_widget.model().index(start_row, start_col))
                    end_rect = self.table_widget.visualRect(self.table_widget.model().index(end_row, end_col))

                    start_x = start_rect.center().x()
                    start_y = start_rect.center().y()
                    end_x = end_rect.center().x()
                    end_y = end_rect.center().y()

                    # 计算线条斜率以延伸线条到表格边缘
                    if end_x != start_x:  # 避免除以零错误
                        slope = (end_y - start_y) / (end_x - start_x)

                        # 计算延伸到左边界的点
                        left_x = 0
                        left_y = start_y - slope * (start_x - left_x)

                        # 计算延伸到右边界的点
                        right_x = view_rect.width()
                        right_y = end_y + slope * (right_x - end_x)

                        # 检查是否是水平趋势线(几乎水平)
                        if abs(slope) < 0.05:
                            # 水平线，直接使用从左到右的边界
                            start_x = 0
                            end_x = view_rect.width()
                        else:
                            # 非水平线，检查边界点是否在视图区域内
                            if 0 <= left_y <= view_rect.height():
                                start_x = left_x
                                start_y = left_y

                            if 0 <= right_y <= view_rect.height():
                                end_x = right_x
                                end_y = right_y

                    # 绘制趋势线
                    painter.drawLine(start_x, start_y, end_x, end_y)

                    # 添加标签
                    if 'name' in trendline and trendline['name']:
                        font = painter.font()
                        font.setBold(True)
                        painter.setFont(font)

                        # 决定标签位置：在线的上方或下方中点位置
                        label_x = (start_x + end_x) / 2

                        # 创建标签背景以提高可读性
                        label_text = trendline['name']
                        text_rect = painter.fontMetrics().boundingRect(label_text)

                        # 决定标签位置，避免与线重叠
                        if end_y > start_y:  # 下降趋势，将标签放在线上方
                            label_y = (start_y + end_y) / 2 - 10
                            bg_rect = QRect(label_x - text_rect.width()/2 - 5,
                                            label_y - text_rect.height() + 3,
                                            text_rect.width() + 10,
                                            text_rect.height() + 5)
                        else:  # 上升趋势，将标签放在线下方
                            label_y = (start_y + end_y) / 2 + 15
                            bg_rect = QRect(label_x - text_rect.width()/2 - 5,
                                            label_y - text_rect.height() - 2,
                                            text_rect.width() + 10,
                                            text_rect.height() + 5)

                        # 绘制半透明背景
                        bg_color = QColor(self.background_color)
                        bg_color.setAlpha(180)  # 半透明背景
                        painter.fillRect(bg_rect, bg_color)

                        # 绘制标签
                        painter.drawText(QPointF(label_x - text_rect.width()/2, label_y), label_text)

                except Exception as e:
                    print(f"绘制趋势线错误: {e}")
                    import traceback
                    traceback.print_exc()

            painter.end()

        except Exception as e:
            print(f"绘制趋势线主函数错误: {e}")
            import traceback
            traceback.print_exc()

    def _draw_support_resistance_levels(self):
        """绘制支撑/阻力位"""
        if not hasattr(self, 'support_resistance_levels') or not self.support_resistance_levels:
            return

        try:
            # 创建Qt绘图器
            viewport = self.table_widget.viewport()
            painter = QPainter(viewport)
            painter.setRenderHint(QPainter.Antialiasing)

            # 设置字体
            label_font = QFont()
            label_font.setBold(True)
            label_font.setPointSize(9)  # 更小的字体
            painter.setFont(label_font)

            # 获取表格几何信息
            header_height = self.table_widget.horizontalHeader().height()
            price_col_width = self.table_widget.verticalHeader().width()
            viewport_width = viewport.width()

            # 绘制每个支撑/阻力位
            for level in self.support_resistance_levels:
                # 获取价格和类型
                price = level['price']
                level_type = level['type']

                # 查找价格对应的行索引
                row = -1

                # 找到最接近的价格行
                for i in range(self.table_widget.rowCount()):
                    price_text = self.table_widget.verticalHeaderItem(i).text()
                    if not price_text:
                        continue
                    row_price = float(price_text)
                    if abs(row_price - price) < self.box_size / 2:
                        row = i
                        break

                # 如果找不到精确匹配，使用最近的价格
                if row == -1:
                    min_diff = float('inf')
                    for i in range(self.table_widget.rowCount()):
                        price_text = self.table_widget.verticalHeaderItem(i).text()
                        if not price_text:
                            continue
                        row_price = float(price_text)
                        diff = abs(row_price - price)
                        if diff < min_diff:
                            min_diff = diff
                            row = i

                # 如果仍然找不到行，跳过这个级别
                if row == -1:
                    continue

                # 获取该行的矩形
                row_rect = self.table_widget.visualRect(self.table_widget.model().index(row, 0))
                y = row_rect.center().y()

                # 设置画笔
                pen = QPen()
                pen.setWidth(2)

                # 根据类型设置颜色
                if level_type == "支撑位":
                    pen.setColor(QColor(0, 150, 0))  # 绿色
                elif level_type == "阻力位":
                    pen.setColor(QColor(200, 0, 0))  # 红色
                else:  # 支撑/阻力
                    pen.setColor(QColor(0, 0, 200))  # 蓝色

                # 根据强度设置线型
                if level.get('strength', '') == "强":
                    pen.setStyle(Qt.SolidLine)
                elif level.get('strength', '') == "中":
                    pen.setStyle(Qt.DashLine)
                else:  # "弱"
                    pen.setStyle(Qt.DotLine)

                painter.setPen(pen)

                # 绘制水平线 - 从价格列右侧开始，横贯整个表格
                painter.drawLine(price_col_width, y, viewport_width, y)

                # 绘制标签
                label_text = f"{level_type} ({price})"

                # 创建标签背景以提高可读性
                text_rect = painter.fontMetrics().boundingRect(label_text)
                bg_rect = QRect(price_col_width + 5, y - text_rect.height()/2 - 3,
                                text_rect.width() + 10, text_rect.height() + 6)

                # 绘制半透明背景
                bg_color = QColor(self.background_color)
                bg_color.setAlpha(180)  # 半透明背景
                painter.fillRect(bg_rect, bg_color)

                # 绘制文本
                painter.drawText(price_col_width + 10, y + text_rect.height()/2 - 3, label_text)

            painter.end()

        except Exception as e:
            print(f"绘制支撑/阻力位出错: {e}")
            import traceback
            traceback.print_exc()

    def _set_background_color(self):
        """设置背景色"""
        color = QColorDialog.getColor(self.background_color, self, "选择背景色")
        if color.isValid():
            self.background_color = color
            self.bg_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")
            self._update_table()  # 更新表格样式

    def _set_grid_color(self):
        """设置网格线颜色"""
        color = QColorDialog.getColor(self.grid_color, self, "选择网格线颜色")
        if color.isValid():
            self.grid_color = color
            self.grid_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")

            # 更新表格样式
            self.table_widget.setStyleSheet(f"""
                QTableWidget {{
                    background-color: {self.background_color.name()};
                    gridline-color: {self.grid_color.name()};
                }}
            """)

            # 强制表格重绘
            self.table_widget.viewport().update()

    def _set_x_color(self):
        """设置X符号颜色"""
        color = QColorDialog.getColor(self.x_symbol_color, self, "选择X符号颜色")
        if color.isValid():
            self.x_symbol_color = color
            self.x_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")
            self._update_table()  # 更新表格样式

    def _set_o_color(self):
        """设置O符号颜色"""
        color = QColorDialog.getColor(self.o_symbol_color, self, "选择O符号颜色")
        if color.isValid():
            self.o_symbol_color = color
            self.o_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")
            self._update_table()  # 更新表格样式

    def _set_grid_style(self):
        """设置网格线样式"""
        style_index = self.grid_style_combo.currentIndex()
        if style_index == 0:
            self.grid_style = Qt.SolidLine
        elif style_index == 1:
            self.grid_style = Qt.DashLine
        elif style_index == 2:
            self.grid_style = Qt.DotLine
        elif style_index == 3:
            self.grid_style = Qt.DashDotLine

        # 更新表格
        self._update_table()

    def _restore_default_settings(self):
        """恢复默认设置"""
        try:
            # 从chart_controller的settings对象加载设置

            # 获取chart部分设置
            box_size = self.chart_controller.settings.get_setting("chart", "box_size", 1.0)
            reversal_amount = self.chart_controller.settings.get_setting("chart", "reversal_amount", 3)
            method_value = self.chart_controller.settings.get_setting("chart", "method", "high_low")
            auto_box_size = self.chart_controller.settings.get_setting("chart", "auto_box_size", True)
            volume_scale = self.chart_controller.settings.get_setting("chart", "volume_scale", 500.0)

            # 获取UI部分设置 - 使用正确的键名
            # 首先尝试使用chart_bg_color，如果不存在则使用background_color
            background_color = self.chart_controller.settings.get_setting("ui", "chart_bg_color", None)
            if background_color is None:
                background_color = self.chart_controller.settings.get_setting("ui", "background_color", "#e5f5e5")

            grid_color = self.chart_controller.settings.get_setting("ui", "chart_grid_color", "#CCCCCC")

            # 首先尝试使用chart_x_color，如果不存在则使用x_symbol_color
            x_color = self.chart_controller.settings.get_setting("ui", "chart_x_color", None)
            if x_color is None:
                x_color = self.chart_controller.settings.get_setting("ui", "x_symbol_color", "#FF4444")

            # 首先尝试使用chart_o_color，如果不存在则使用o_symbol_color
            o_color = self.chart_controller.settings.get_setting("ui", "chart_o_color", None)
            if o_color is None:
                o_color = self.chart_controller.settings.get_setting("ui", "o_symbol_color", "#4444FF")

            # 成交量颜色设置 - 尝试多个可能的键名
            volume_up_color = self.chart_controller.settings.get_setting("ui", "volume_increase_color", None)
            if volume_up_color is None:
                volume_up_color = self.chart_controller.settings.get_setting("ui", "volume_up_color", "#FF0000")

            volume_down_color = self.chart_controller.settings.get_setting("ui", "volume_decrease_color", None)
            if volume_down_color is None:
                volume_down_color = self.chart_controller.settings.get_setting("ui", "volume_down_color", "#008000")

            # 获取显示设置 - 尝试多个可能的键名
            show_volume = self.chart_controller.settings.get_setting("ui", "show_volume_chart", None)
            if show_volume is None:
                show_volume = self.chart_controller.settings.get_setting("ui", "show_volume", True)

            # 获取网格样式 - 优先使用数字索引
            grid_style_index = self.chart_controller.settings.get_setting("ui", "grid_style", 0)

            # 应用设置到UI
            # 格子大小设置
            self.box_size_spin.setValue(box_size)

            # 自动格值计算设置
            self.auto_box_size = auto_box_size
            self.auto_box_size_check.setChecked(auto_box_size)
            self.box_size_spin.setEnabled(not auto_box_size)

            # 反转数量
            self.reversal_spin.setValue(reversal_amount)

            # 绘制方法 - 转换为界面显示值
            method_text = "高低价法" if method_value == "high_low" else "收盘价法"
            self.method_combo.setCurrentText(method_text)

            # 颜色设置
            self.background_color = QColor(background_color)
            self.bg_color_button.setStyleSheet(f"background-color: {self.background_color.name()}; border: 1px solid #888;")

            self.grid_color = QColor(grid_color)
            self.grid_color_button.setStyleSheet(f"background-color: {self.grid_color.name()}; border: 1px solid #888;")

            self.x_symbol_color = QColor(x_color)
            self.x_color_button.setStyleSheet(f"background-color: {self.x_symbol_color.name()}; border: 1px solid #888;")

            self.o_symbol_color = QColor(o_color)
            self.o_color_button.setStyleSheet(f"background-color: {self.o_symbol_color.name()}; border: 1px solid #888;")

            # 成交量颜色设置
            self.volume_up_color = QColor(volume_up_color)
            self.vol_up_color_button.setStyleSheet(f"background-color: {self.volume_up_color.name()}; border: 1px solid #888;")

            self.volume_down_color = QColor(volume_down_color)
            self.vol_down_color_button.setStyleSheet(f"background-color: {self.volume_down_color.name()}; border: 1px solid #888;")

            # 网格样式设置
            self.grid_style_combo.setCurrentIndex(grid_style_index)

            # 成交量单位设置
            self.volume_scale_spin.setValue(volume_scale)

            # 成交量显示设置
            self.show_volume_check.setChecked(show_volume)

            # 获取设置文件路径
            settings_file_path = self.chart_controller.settings.settings_file

            # 显示成功消息
            QMessageBox.information(
                self,
                "提示",
                f"已恢复默认设置，点击应用设置按钮生效\n\n设置文件位置: {settings_file_path}"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"恢复默认设置时出错: {str(e)}")
            print(f"恢复默认设置时出错: {str(e)}")

    def _set_volume_up_color(self):
        """设置上涨成交量颜色"""
        color = QColorDialog.getColor(self.volume_up_color, self, "选择上涨成交量颜色")
        if color.isValid():
            self.volume_up_color = color
            self.vol_up_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")
            self._update_table()  # 更新表格样式

    def _set_volume_down_color(self):
        """设置下跌成交量颜色"""
        color = QColorDialog.getColor(self.volume_down_color, self, "选择下跌成交量颜色")
        if color.isValid():
            self.volume_down_color = color
            self.vol_down_color_button.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #888;")
            self._update_table()  # 更新表格样式

    def _toggle_volume_chart(self, state):
        """切换成交量子图的显示/隐藏状态"""
        # 更新成交量显示状态
        self.show_volume_chart = (state == Qt.Checked)

        # 更新设置字典
        self.settings["show_volume"] = self.show_volume_chart
        
        # 保存成交量显示设置到设置管理器
        self.chart_controller.settings.set_setting("ui", "show_volume_chart", self.show_volume_chart)
        self.chart_controller.settings.save_settings()
        print(f"[PointFigureChart] 成交量显示设置已保存: {self.show_volume_chart}")

        # 获取分割器中的成交量容器
        volume_container = self.splitter.widget(1)

        if self.show_volume_chart:
            # 显示成交量子图
            volume_container.show()

            # 获取当前的分割器大小
            sizes = self.splitter.sizes()

            # 如果成交量部分完全被收起（大小为0），则设置合理的初始比例
            if len(sizes) > 1 and sizes[1] == 0:
                # 记录当前总高度
                total_height = sum(sizes)
                # 根据3:1的比例计算新的大小
                new_sizes = [int(total_height * 0.75), int(total_height * 0.25)]
                self.splitter.setSizes(new_sizes)
        else:
            # 隐藏成交量子图
            volume_container.hide()

        # 更新界面
        self.splitter.update()

        # 提示用户
        if hasattr(self.parent(), 'statusBar'):
            status_message = "成交量子图已显示" if self.show_volume_chart else "成交量子图已隐藏"
            self.parent().statusBar().showMessage(status_message, 3000)

    def _volume_table_wheel_event(self, event):
        """处理成交量表格的滚轮事件，转发到主表格处理，保持滚动同步"""
        # 直接调用主表格的滚轮事件处理，保持一致的行为
        self._table_wheel_event(event)

    def _handle_selection_changed(self):
        """处理表格选择变化事件"""
        selected_ranges = self.table_widget.selectedRanges()

        # 获取主窗口
        main_window = self.window()
        if not hasattr(main_window, 'statusBar'):
            return

        # 获取当前状态栏信息
        current_message = main_window.statusBar().currentMessage()

        # 如果没有选择，则清除选择信息标签
        if not selected_ranges:
            # 查找是否有选择信息标签
            selection_label = None
            for child in main_window.statusBar().children():
                if isinstance(child, QLabel) and hasattr(child, 'isSelectionInfo') and child.isSelectionInfo:
                    selection_label = child
                    break

            # 如果找到了选择信息标签，隐藏它
            if selection_label:
                selection_label.hide()
            return

        # 计算总行数和总列数
        total_rows = set()
        total_cols = set()

        for selected_range in selected_ranges:
            # 获取行范围
            for row in range(selected_range.topRow(), selected_range.bottomRow() + 1):
                total_rows.add(row)

            # 获取列范围 (减去价格列的第0列)
            for col in range(selected_range.leftColumn(), selected_range.rightColumn() + 1):
                if col > 0:  # 忽略价格列
                    total_cols.add(col)

        # 统计行列数
        row_count = len(total_rows)
        col_count = len(total_cols)

        # 查找是否已有选择信息标签
        selection_label = None
        for child in main_window.statusBar().children():
            if isinstance(child, QLabel) and hasattr(child, 'isSelectionInfo') and child.isSelectionInfo:
                selection_label = child
                break

        # 如果没有找到，创建一个新的标签
        if not selection_label:
            selection_label = QLabel()
            # 使用自定义属性标记这是选择信息标签
            selection_label.isSelectionInfo = True
            main_window.statusBar().addPermanentWidget(selection_label)

        # 设置标签文本和样式
        selection_text = f"| 选中行数: {row_count}行 选中列数: {col_count}列"
        selection_label.setText(selection_text)
        selection_label.setStyleSheet("font-weight: bold; color: blue;")
        selection_label.show()

    def _validate_selection_for_target_calculation(self):
        """验证选择是否符合目标价计算的要求

        要求：
        1. 必须选择至少一个区域
        2. 选择必须在同一行
        3. 选择必须至少包含3个连续单元格

        返回：
        tuple: (验证是否通过, 错误信息, 选择区域对象)
        """
        # 获取选择的区域
        selected_ranges = self.table_widget.selectedRanges()

        # 验证是否有选择
        if not selected_ranges:
            return False, "请先选择一个区域", None

        # 获取第一个选择区域
        selection = selected_ranges[0]

        # 验证是否在同一行
        if selection.rowCount() != 1:
            return False, "请只选择同一行的单元格", None

        # 验证列数量是否至少为3
        left_col = selection.leftColumn()
        right_col = selection.rightColumn()

        # 如果包含第0列(价格列)，调整实际的数据列数量计算
        data_col_count = right_col - left_col + 1
        if left_col == 0:
            data_col_count -= 1  # 减去价格列

        if data_col_count < 3:
            return False, "请至少选择连续的3个数据单元格", None

        return True, "", selection

    def _calculate_upward_target(self):
        """计算上行目标价位"""
        try:
            # 验证选择是否符合要求
            is_valid, error_message, selection = self._validate_selection_for_target_calculation()
            if not is_valid:
                QMessageBox.warning(self, "警告", error_message)
                return

            # 获取选择区域的行和列范围
            # 注意第0列是价格列，从第1列开始才是数据
            top_row = selection.topRow()
            bottom_row = selection.bottomRow()
            left_col = selection.leftColumn()
            right_col = selection.rightColumn()

            # 确保至少选择了一列数据
            if left_col == 0 and right_col == 0:
                QMessageBox.warning(self, "警告", "请选择至少一列数据（不只是价格列）")
                return

            # 调整列索引，跳过价格列
            if left_col == 0:
                left_col = 1

            # 计算选择区域的宽度和高度
            width = right_col - left_col + 1
            height = bottom_row - top_row + 1

            # 获取格子值
            box_size = self.chart_controller.box_size
            reversal_amount = self.chart_controller.reversal_amount

            # 计算最低点的价格 - 修改逻辑，确保找到所选列中的最低价格点
            min_price = float('inf')
            min_row = -1

            # 记录所选列中有X或O的所有行号
            rows_with_marks = set()

            # 首先，遍历所选列，找出所有包含X或O的行
            for col in range(left_col, right_col + 1):
                for row in range(self.table_widget.rowCount()):  # 遍历所有行
                    item = self.table_widget.item(row, col)
                    if item and item.text() in ('X', 'O'):
                        rows_with_marks.add(row)

            # 然后，在这些行中找出价格最低的
            for row in rows_with_marks:
                price_item = self.table_widget.item(row, 0)
                if price_item:
                    try:
                        price_text = price_item.text().strip()
                        # 移除可能存在的标记
                        for marker in ["▲", "△", "▼", "▽"]:
                            price_text = price_text.replace(marker, "").strip()
                        # 价格列现在显示的是实际价格，不需要乘以box_size
                        price = float(price_text)
                        if price < min_price:
                            min_price = price
                            min_row = row
                    except ValueError:
                        pass

            # 如果没有找到最低点
            if min_price == float('inf'):
                QMessageBox.warning(self, "警告", "在选择区域内没有找到有效点")
                return

            # 获取选中行的价格（这是上涨最大目标位的基准）
            selected_price = None
            # 使用选中区域最后一行作为选中行
            selected_row = bottom_row
            price_item = self.table_widget.item(selected_row, 0)
            if price_item:
                try:
                    price_text = price_item.text().strip()
                    # 移除可能存在的标记
                    for marker in ["▲", "△", "▼", "▽"]:
                        price_text = price_text.replace(marker, "").strip()
                    # 价格列现在显示的是实际价格，不需要乘以box_size
                    selected_price = float(price_text)
                except ValueError:
                    pass

            if selected_price is None:
                QMessageBox.warning(self, "警告", "无法获取选中行的价格")
                return

            # 获取列范围的中心位置（用于绘制）
            center_col = (left_col + right_col) // 2

            # 直接保存选择区域信息（用于后续绘制）
            self.selected_range = {
                'row': selected_row,
                'start_col': left_col,
                'end_col': right_col
            }
            print(f"保存选择区域信息: {self.selected_range}")

            # 高亮显示选择区域
            self._highlight_target_region(selected_row, left_col, right_col)

            # 上行目标位计算:
            # 根据快捷键测目标位对话.md的描述:
            # 上涨最大目标位：选中行所对应的价格 + (格子数量 × 格值 × 转向值)
            max_target = selected_price + (width * box_size * reversal_amount)
            # 上涨最小目标位：区域所有列里面的最小价格 + (格子数量 × 格值 × 转向值)
            min_target = min_price + (width * box_size * reversal_amount)

            # 设置目标位线 (注意参数顺序：min_target, max_target)
            self._draw_target_lines(min_target, max_target, True)

            # 添加定时器定期重绘目标线，防止闪烁
            for delay in [2000, 4000, 6000, 8000, 10000, 15000, 20000, 25000]:
                QTimer.singleShot(delay, self._auto_redraw_targets)

            # 显示结果对话框
            msg = f"上行目标价位计算结果:\n\n"
            msg += f"选中行数: {height}\n"
            msg += f"选中列数: {width}\n"
            msg += f"格值: {box_size}\n"
            msg += f"转向格: {reversal_amount}\n\n"
            msg += f"选中行价格: {selected_price:.2f}\n"
            msg += f"区域最低价格: {min_price:.2f}\n"
            msg += f"最大目标位: {max_target:.2f}\n"
            msg += f"最小目标位: {min_target:.2f}"

            QMessageBox.information(self, "上行目标价位", msg)

        except Exception as e:
            error_msg = f"计算上行目标价位时出错: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            import traceback
            traceback.print_exc()

    def _calculate_downward_target(self):
        """计算下行目标价位"""
        try:
            # 验证选择是否符合要求
            is_valid, error_message, selection = self._validate_selection_for_target_calculation()
            if not is_valid:
                QMessageBox.warning(self, "警告", error_message)
                return

            # 获取选择区域的行和列范围
            # 注意第0列是价格列，从第1列开始才是数据
            top_row = selection.topRow()
            bottom_row = selection.bottomRow()
            left_col = selection.leftColumn()
            right_col = selection.rightColumn()

            # 确保至少选择了一列数据
            if left_col == 0 and right_col == 0:
                QMessageBox.warning(self, "警告", "请选择至少一列数据（不只是价格列）")
                return

            # 调整列索引，跳过价格列
            if left_col == 0:
                left_col = 1

            # 计算选择区域的宽度和高度
            width = right_col - left_col + 1
            height = bottom_row - top_row + 1

            # 获取格子值
            box_size = self.chart_controller.box_size
            reversal_amount = self.chart_controller.reversal_amount

            # 计算最高点的价格 - 修改逻辑，确保找到所选列中的最高价格点
            max_price = -float('inf')
            max_row = -1

            # 记录所选列中有X或O的所有行号
            rows_with_marks = set()

            # 首先，遍历所选列，找出所有包含X或O的行
            for col in range(left_col, right_col + 1):
                for row in range(self.table_widget.rowCount()):  # 遍历所有行
                    item = self.table_widget.item(row, col)
                    if item and item.text() in ('X', 'O'):
                        rows_with_marks.add(row)

            # 然后，在这些行中找出价格最高的
            for row in rows_with_marks:
                price_item = self.table_widget.item(row, 0)
                if price_item:
                    try:
                        price_text = price_item.text().strip()
                        # 移除可能存在的标记
                        for marker in ["▲", "△", "▼", "▽"]:
                            price_text = price_text.replace(marker, "").strip()
                        # 价格列现在显示的是实际价格，不需要乘以box_size
                        price = float(price_text)
                        if price > max_price:
                            max_price = price
                            max_row = row
                    except ValueError:
                        pass

            # 如果没有找到最高点
            if max_price == -float('inf'):
                QMessageBox.warning(self, "警告", "在选择区域内没有找到有效点")
                return

            # 获取选中行的价格（这是下跌最大目标位的基准）
            selected_price = None
            # 使用选中区域第一行作为选中行
            selected_row = top_row
            price_item = self.table_widget.item(selected_row, 0)
            if price_item:
                try:
                    price_text = price_item.text().strip()
                    # 移除可能存在的标记
                    for marker in ["▲", "△", "▼", "▽"]:
                        price_text = price_text.replace(marker, "").strip()
                    # 价格列现在显示的是实际价格，不需要乘以box_size
                    selected_price = float(price_text)
                except ValueError:
                    pass

            if selected_price is None:
                QMessageBox.warning(self, "警告", "无法获取选中行的价格")
                return

            # 获取列范围的中心位置（用于绘制）
            center_col = (left_col + right_col) // 2

            # 直接保存选择区域信息（用于后续绘制）
            self.selected_range = {
                'row': selected_row,
                'start_col': left_col,
                'end_col': right_col
            }
            print(f"保存选择区域信息: {self.selected_range}")

            # 高亮显示选择区域
            self._highlight_target_region(selected_row, left_col, right_col)

            # 下行目标位计算:
            # 按照快捷键测目标位对话.md的要求调整计算逻辑
            # 下跌最大目标位 = 选中行所对应的价格 - (格子数量 × 格值 × 转向值)
            max_target = selected_price - (width * box_size * reversal_amount)
            # 下跌最小目标位 = 区域所有列里面的最大价格 - (格子数量 × 格值 × 转向值)
            min_target = max_price - (width * box_size * reversal_amount)

            # 设置目标位线 (注意参数顺序：min_target, max_target)
            self._draw_target_lines(min_target, max_target, False)

            # 添加定时器定期重绘目标线，防止闪烁
            for delay in [2000, 4000, 6000, 8000, 10000, 15000, 20000, 25000]:
                QTimer.singleShot(delay, self._auto_redraw_targets)

            # 显示结果对话框
            msg = f"下跌目标价位计算结果:\n\n"
            msg += f"选中行数: {height}\n"
            msg += f"选中列数: {width}\n"
            msg += f"格值: {box_size}\n"
            msg += f"转向格: {reversal_amount}\n\n"
            msg += f"选中行价格: {selected_price:.2f}\n"
            msg += f"区域最高价格: {max_price:.2f}\n"
            msg += f"最大目标位: {max_target:.2f}\n"
            msg += f"最小目标位: {min_target:.2f}"

            QMessageBox.information(self, "下跌目标价位", msg)

        except Exception as e:
            error_msg = f"计算下行目标价位时出错: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            import traceback
            traceback.print_exc()

    def _highlight_target_region(self, row, start_col, end_col):
        """高亮显示目标区域的单元格"""
        try:
            # 确保参数有效
            if self.table_widget is None:
                return

            if row < 0 or row >= self.table_widget.rowCount():
                return

            # 调整列范围到有效范围内
            start_col = max(0, start_col)
            end_col = min(end_col, self.table_widget.columnCount() - 1)

            # 获取较暗的黄色背景
            background_color = QColor(255, 255, 150)  # 浅黄色

            # 高亮显示所选区域的所有单元格
            for col in range(start_col, end_col + 1):
                item = self.table_widget.item(row, col)
                if item is not None:
                    # 保存原始文本
                    text = item.text()

                    # 设置背景色
                    item.setBackground(background_color)

                    # 如果需要，可以在这里添加边框
                    # item.setForeground(QColor(255, 0, 0))  # 红色文本

            # 保存选择区域信息（用于目标位线绘制）
            self.selected_range = {
                'row': row,
                'start_col': start_col,
                'end_col': end_col
            }

            print(f"已高亮目标区域: 行={row}, 列范围={start_col}-{end_col}")
            print(f"已保存选择区域信息: {self.selected_range}")

        except Exception as e:
            print(f"高亮目标区域出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _expand_table_for_targets(self, min_target, max_target):
        """扩展表格以确保目标位可以显示"""
        try:
            if self.chart_data is None or self.chart_data.empty:
                return

            # 获取当前价格范围
            current_prices = self.chart_data.index.tolist()
            current_min_price = min(current_prices)
            current_max_price = max(current_prices)

            # 获取格值
            box_size = self.chart_controller.box_size

            # 计算需要扩展的价格范围
            target_min_price = min(min_target, max_target)
            target_max_price = max(min_target, max_target)

            # 添加缓冲区，确保目标位线有足够的显示空间
            buffer_boxes = 5  # 在目标位上下各增加5个格子的缓冲
            buffer_amount = buffer_boxes * box_size

            # 计算新的价格范围
            new_min_price = min(current_min_price, target_min_price - buffer_amount)
            new_max_price = max(current_max_price, target_max_price + buffer_amount)

            # 生成新的价格序列，确保价格对齐到格值
            new_prices = []

            # 将价格对齐到格值边界
            aligned_min_price = box_size * round(new_min_price / box_size)
            aligned_max_price = box_size * round(new_max_price / box_size)

            # 确保范围足够大
            if aligned_min_price > new_min_price:
                aligned_min_price -= box_size
            if aligned_max_price < new_max_price:
                aligned_max_price += box_size

            current_price = aligned_min_price
            while current_price <= aligned_max_price + 1e-10:  # 添加小误差避免浮点数比较问题
                # 格式化价格以避免浮点数精度问题
                if box_size >= 1:
                    formatted_price = round(current_price, 0)
                elif box_size >= 0.1:
                    formatted_price = round(current_price, 1)
                elif box_size >= 0.01:
                    formatted_price = round(current_price, 2)
                else:
                    formatted_price = round(current_price, 3)

                new_prices.append(formatted_price)
                current_price += box_size

            # 确保价格序列按降序排列（从高到低），与现有表格结构一致
            new_prices.sort(reverse=True)

            # 检查是否需要扩展
            price_range_changed = (aligned_min_price < current_min_price) or (aligned_max_price > current_max_price)

            # 扩展列数（右侧方向）- 从设置中获取扩展列数
            from utils.settings_manager import SettingsManager
            settings = SettingsManager()
            extra_cols = settings.get_setting("table_expansion", "expand_right_columns", 10)

            current_cols = len(self.chart_data.columns)
            new_cols = current_cols + extra_cols

            if price_range_changed or new_cols > current_cols:
                print(f"扩展表格: 价格范围 {current_min_price:.2f}-{current_max_price:.2f} -> {new_min_price:.2f}-{new_max_price:.2f}")
                print(f"扩展表格: 列数 {current_cols} -> {new_cols}")

                # 创建新的扩展数据框
                extended_data = pd.DataFrame(index=new_prices, columns=range(new_cols))

                # 复制原有数据到新数据框中
                for price in current_prices:
                    if price in extended_data.index:
                        for col in range(current_cols):
                            if col in self.chart_data.columns:
                                extended_data.loc[price, col] = self.chart_data.loc[price, col]

                # 复制成交量信息（如果存在）
                if hasattr(self.chart_data, 'attrs') and 'volume_info' in self.chart_data.attrs:
                    volume_info = self.chart_data.attrs['volume_info'].copy()
                    # 为新列添加空的成交量数据
                    for col in range(current_cols, new_cols):
                        volume_info[col] = pd.Series(dtype=float)
                    extended_data.attrs = {'volume_info': volume_info}

                # 更新图表数据
                self.chart_data = extended_data

                # 重新更新表格显示
                self._update_table()

                print(f"表格扩展完成: 新价格范围包含 {len(new_prices)} 行，{new_cols} 列")

        except Exception as e:
            print(f"扩展表格出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _check_targets_in_range(self, min_target, max_target):
        """检查目标位是否在当前表格价格范围内"""
        try:
            if self.chart_data is None or self.chart_data.empty:
                return False

            current_prices = self.chart_data.index.tolist()
            current_min_price = min(current_prices)
            current_max_price = max(current_prices)

            # 检查目标位是否在当前价格范围内
            target_min_price = min(min_target, max_target)
            target_max_price = max(min_target, max_target)

            return (target_min_price >= current_min_price and
                   target_max_price <= current_max_price)

        except Exception as e:
            print(f"检查目标位范围出错: {str(e)}")
            return False

    def _draw_target_lines(self, min_target, max_target, is_upward=True):
        """在图表上绘制目标位线"""
        # 保存目标位
        self.target_lines = {
            'min': min_target,
            'max': max_target,
            'is_upward': is_upward
        }

        try:
            # 检查目标位是否超出当前表格范围，如果超出则提示用户
            if not self._check_targets_in_range(min_target, max_target):
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示",
                    f"目标位 {min_target:.2f} - {max_target:.2f} 超出当前表格显示范围。\n"
                    "建议重新生成点数图以获得更大的价格范围显示。")

            # 禁用缩放功能，直到目标线被清除
            self.zoom_disabled = True

            # 更新价格映射用于自定义委托
            price_map = {}
            for row in range(self.table_widget.rowCount()):
                header_item = self.table_widget.verticalHeaderItem(row)
                if header_item is not None:
                    try:
                        price = float(header_item.text())
                        price_map[row] = price
                    except (ValueError, TypeError):
                        pass

            # 应用自定义委托
            if hasattr(self, 'target_line_delegate'):
                self.target_line_delegate.set_target_lines(
                    self.target_lines,
                    price_map,
                    self.table_widget.rowHeight(0) if self.table_widget.rowCount() > 0 else 20
                )

            # 确保主线程处理
            # 执行多次尝试，增加绘制成功概率
            self._draw_target_position_lines()

            # 使用多个定时器确保UI线程有机会绘制
            # 短延迟重绘
            QTimer.singleShot(100, self._draw_overlays)
            QTimer.singleShot(200, self._draw_target_position_lines)

            # 中等延迟重绘
            QTimer.singleShot(500, self._draw_overlays)
            QTimer.singleShot(600, self._draw_target_position_lines)

            # 长延迟重绘
            QTimer.singleShot(1000, self._draw_overlays)
            QTimer.singleShot(1200, self._draw_target_position_lines)

            # 直接调用repaint强制立即重绘，而不是update
            self.table_widget.viewport().repaint()

            # 记录绘制状态
            print(f"目标位已设置 - 最小:{min_target:.2f} 最大:{max_target:.2f} 方向:{'上行' if is_upward else '下行'}")

        except Exception as e:
            print(f"设置目标位时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _draw_target_position_lines(self):
        """绘制目标位置线"""
        if not hasattr(self, 'target_lines') or not self.target_lines:
            return

        # 防止短时间内重复绘制
        import time
        current_time = time.time()
        if hasattr(self, 'drawing_target_lines') and self.drawing_target_lines:
            print("正在绘制目标线，跳过重复绘制")
            return

        if hasattr(self, 'last_draw_time') and (current_time - self.last_draw_time < 0.5):
            print(f"距离上次绘制时间太短({current_time - self.last_draw_time:.2f}秒)，跳过")
            # 确保之前绘制的目标线是可见的
            self._ensure_overlays_visible()
            return

        self.drawing_target_lines = True
        try:
            # 在绘制新的目标线之前，先清除现有的目标线
            self._clear_target_overlays()

            # 检查表格是否有效
            if self.table_widget is None or self.table_widget.rowCount() == 0 or self.table_widget.columnCount() == 0:
                print("绘制失败: 表格为空或无效")
                return

            # 获取目标位置信息
            min_target = self.target_lines['min']
            max_target = self.target_lines['max']
            is_upward = self.target_lines['is_upward']

            print(f"开始绘制目标位线: min={min_target:.2f}, max={max_target:.2f}, 方向={'上行' if is_upward else '下行'}")

            # 初始化目标位线叠加层列表（如果不存在）
            if not hasattr(self, 'target_overlays'):
                self.target_overlays = []

            # 创建新的透明叠加层
            print("创建新的目标位线叠加层")
            target_overlay = TargetOverlay(self.table_widget)

            # 获取表格视口的几何信息
            viewport = self.table_widget.viewport()
            viewport_rect = viewport.rect()
            print(f"表格视口大小: 宽={viewport_rect.width()}, 高={viewport_rect.height()}")

            # 获取表格滚动位置
            h_scroll = self.table_widget.horizontalScrollBar().value()
            v_scroll = self.table_widget.verticalScrollBar().value()
            print(f"表格滚动位置: 水平={h_scroll}, 垂直={v_scroll}")

            # 设置叠加层的位置和大小 - 关键修改: 使用viewport的全局坐标
            viewport_global_pos = viewport.mapToGlobal(QPoint(0, 0))
            target_global_pos = self.table_widget.mapToGlobal(QPoint(0, 0))
            print(f"视口全局位置: x={viewport_global_pos.x()}, y={viewport_global_pos.y()}")
            print(f"表格全局位置: x={target_global_pos.x()}, y={target_global_pos.y()}")

            # 直接将叠加层放置在viewport上，绝对定位
            target_overlay.setParent(viewport)
            target_overlay.setGeometry(0, 0, viewport_rect.width(), viewport_rect.height())

            # 存储滚动信息，供叠加层使用
            target_overlay.set_scroll_info(h_scroll, v_scroll)

            # 获取行和价格的映射
            price_to_row = {}
            row_to_price = {}

            # 从价格列获取价格信息
            for row in range(self.table_widget.rowCount()):
                price_item = self.table_widget.item(row, 0)
                if price_item is not None:
                    try:
                        # 获取价格文本
                        price_text = price_item.text().strip()
                        # 移除可能的标记
                        for marker in ["▲", "△", "▼", "▽"]:
                            price_text = price_text.replace(marker, "").strip()
                        # 价格列现在显示的是实际价格，不需要转换
                        price = float(price_text)

                        price_to_row[price] = row
                        row_to_price[row] = price
                    except (ValueError, TypeError):
                        pass

            print(f"获取到价格行映射: {len(price_to_row)}行")

            # 查找最接近目标价格的行
            min_row = max_row = -1
            min_price = max_price = 0

            # 查找最小目标对应的行
            min_diff = float('inf')
            for price, row in price_to_row.items():
                diff = abs(price - min_target)
                if diff < min_diff:
                    min_diff = diff
                    min_row = row
                    min_price = price

            # 查找最大目标对应的行
            max_diff = float('inf')
            for price, row in price_to_row.items():
                diff = abs(price - max_target)
                if diff < max_diff:
                    max_diff = diff
                    max_row = row
                    max_price = price

            print(f"找到目标行: min_row={min_row}, max_row={max_row}")

            # 如果没有找到目标行，直接返回
            if min_row < 0 or max_row < 0:
                print("未找到目标价格对应的行")
                return

            # 获取选择区域信息
            if hasattr(self, 'selected_range'):
                print(f"选择区域: {self.selected_range}")
                # 获取选择区域的最右侧位置
                selected_row = self.selected_range['row']
                start_col = self.selected_range['start_col']
                end_col = self.selected_range['end_col']

                # 获取选择区域最右侧单元格的右边缘坐标 - 考虑滚动偏移
                cell_item = self.table_widget.item(selected_row, end_col)
                if cell_item is not None:
                    sel_rect = self.table_widget.visualItemRect(cell_item)

                    # 直接根据方向确定引线的起始位置坐标 - 仅考虑视口内相对坐标
                    if is_upward:
                        # 上行目标：从右上角网格线交叉点开始
                        start_x = sel_rect.right()
                        start_y = sel_rect.top()
                    else:
                        # 下行目标：从右下角网格线交叉点开始
                        start_x = sel_rect.right()
                        start_y = sel_rect.bottom()

                    print(f"选择区域最右侧视口相对坐标: x={start_x}, y={start_y}")

                    # 获取目标价格行的中心坐标 - 考虑滚动偏移
                    min_cell = self.table_widget.item(min_row, 0)  # 价格列
                    max_cell = self.table_widget.item(max_row, 0)  # 价格列

                    if min_cell is not None and max_cell is not None:
                        min_rect = self.table_widget.visualItemRect(min_cell)
                        max_rect = self.table_widget.visualItemRect(max_cell)

                        # 使用视口相对坐标 - 最左侧的列位置
                        min_x = 29
                        min_y = min_rect.center().y()
                        max_x = 29
                        max_y = max_rect.center().y()

                        print(f"目标位视口相对坐标: min_x={min_x}, min_y={min_y}, max_x={max_x}, max_y={max_y}")

                        # 设置叠加层绘制参数
                        target_overlay.set_points(
                            start_x, start_y,                   # 起始点（选区最右侧）
                            min_x, min_y,                       # 最小目标点
                            max_x, max_y,                       # 最大目标点
                            min_price, max_price,               # 目标价格
                            is_upward                           # 方向
                        )

                        # 将新叠加层添加到列表并显示
                        self.target_overlays.append(target_overlay)
                        target_overlay.show()
                        target_overlay.raise_()
                        print("目标位线叠加层已显示")
                    else:
                        print("未找到目标价格对应的单元格")
                else:
                    print("未找到选择区域右侧单元格")
            else:
                print("未找到选择区域信息")

            # 使用定时器延迟确保叠加层保持可见
            QTimer.singleShot(100, self._ensure_overlays_visible)
            QTimer.singleShot(500, self._ensure_overlays_visible)
            QTimer.singleShot(1000, self._ensure_overlays_visible)
            QTimer.singleShot(2000, self._ensure_overlays_visible)
            QTimer.singleShot(5000, self._ensure_overlays_visible)
            QTimer.singleShot(10000, self._ensure_overlays_visible)  # 增加更长的延时
            QTimer.singleShot(15000, self._ensure_overlays_visible)  # 增加更长的延时
            QTimer.singleShot(20000, self._ensure_overlays_visible)  # 增加更长的延时
            QTimer.singleShot(30000, self._ensure_overlays_visible)  # 增加更长的延时
            QTimer.singleShot(45000, self._ensure_overlays_visible)
            QTimer.singleShot(60000, self._ensure_overlays_visible)  # 增加到1分钟
            QTimer.singleShot(120000, self._ensure_overlays_visible)  # 增加到2分钟
            QTimer.singleShot(180000, self._ensure_overlays_visible)  # 增加到3分钟
        finally:
            self.drawing_target_lines = False
            self.last_draw_time = time.time()  # 更新最后绘制时间

    def _ensure_overlays_visible(self):
        """确保叠加层可见"""
        if hasattr(self, 'target_overlays') and self.target_overlays:
            for overlay in self.target_overlays:
                if overlay and not overlay.isVisible():
                    overlay.show()
                    overlay.raise_()
                    print("重新显示目标叠加层")

    def _auto_redraw_targets(self):
        """自动重绘目标线，防止闪烁"""
        if hasattr(self, 'target_lines') and self.target_lines:
            # 检查是否应该重绘
            import time
            current_time = time.time()
            if hasattr(self, 'last_draw_time') and (current_time - self.last_draw_time > 2.0):
                print("自动重绘目标线")
                self._draw_target_position_lines()

    def _highlight_target_rows(self, min_target, max_target):
        """通过背景色高亮显示目标位行"""
        try:
            if not hasattr(self, 'target_lines') or not self.target_lines:
                return

            if self.table_widget is None or self.table_widget.rowCount() == 0:
                return

            # 获取价格与行的对应关系
            row_to_price = {}
            for row in range(self.table_widget.rowCount()):
                header_item = self.table_widget.verticalHeaderItem(row)
                if header_item is not None:
                    try:
                        price = float(header_item.text())
                        row_to_price[row] = price
                    except (ValueError, TypeError):
                        pass

            if not row_to_price:
                return

            # 查找最近的行
            min_row = max_row = -1
            min_diff = max_diff = float('inf')

            for row, price in row_to_price.items():
                diff_min = abs(price - min_target)
                if diff_min < min_diff:
                    min_diff = diff_min
                    min_row = row

                diff_max = abs(price - max_target)
                if diff_max < max_diff:
                    max_diff = diff_max
                    max_row = row

            # 高亮显示目标行（通过设置垂直表头背景色）
            if min_row >= 0:
                header_item = self.table_widget.verticalHeaderItem(min_row)
                if header_item is not None:
                    header_item.setBackground(QColor(255, 230, 100))  # 浅黄色
                    print(f"高亮最小目标位行: {min_row}, 价格: {min_target:.2f}")

            if max_row >= 0:
                header_item = self.table_widget.verticalHeaderItem(max_row)
                if header_item is not None:
                    header_item.setBackground(QColor(255, 230, 100))  # 浅黄色
                    print(f"高亮最大目标位行: {max_row}, 价格: {max_target:.2f}")
        except Exception as e:
            print(f"高亮目标行出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _mark_target_with_cell_color(self, min_target, max_target, is_upward):
        """通过单元格背景色标记目标位（作为QPainter失败的备选方案）"""
        if self.table_widget is None or self.table_widget.rowCount() == 0:
            return

        try:
            # 获取行与价格的对应关系
            row_to_price = {}
            price_to_row = {}
            all_prices = []

            for row in range(self.table_widget.rowCount()):
                header_item = self.table_widget.verticalHeaderItem(row)
                if header_item is not None:
                    try:
                        price = float(header_item.text())
                        row_to_price[row] = price
                        price_to_row[price] = row
                        all_prices.append(price)
                    except (ValueError, TypeError):
                        pass

            if not all_prices:
                return

            # 找到最接近目标价格的行
            min_row = max_row = -1

            # 排序价格
            all_prices.sort()

            # 查找最大目标位行
            if max_target in price_to_row:
                max_row = price_to_row[max_target]
            else:
                # 找最近的行
                prices_above = [p for p in all_prices if p >= max_target]
                prices_below = [p for p in all_prices if p <= max_target]

                if prices_above and prices_below:
                    closest_above = min(prices_above)
                    closest_below = max(prices_below)

                    if abs(max_target - closest_above) <= abs(max_target - closest_below):
                        max_row = price_to_row[closest_above]
                    else:
                        max_row = price_to_row[closest_below]
                elif prices_above:
                    max_row = price_to_row[min(prices_above)]
                elif prices_below:
                    max_row = price_to_row[max(prices_below)]

            # 查找最小目标位行
            if min_target in price_to_row:
                min_row = price_to_row[min_target]
            else:
                # 找最近的行
                prices_above = [p for p in all_prices if p >= min_target]
                prices_below = [p for p in all_prices if p <= min_target]

                if prices_above and prices_below:
                    closest_above = min(prices_above)
                    closest_below = max(prices_below)

                    if abs(min_target - closest_above) <= abs(min_target - closest_below):
                        min_row = price_to_row[closest_above]
                    else:
                        min_row = price_to_row[closest_below]
                elif prices_above:
                    min_row = price_to_row[min(prices_above)]
                elif prices_below:
                    min_row = price_to_row[max(prices_below)]

            # 设置背景颜色
            color = QColor(200, 255, 200) if is_upward else QColor(255, 200, 200)  # 绿色或红色

            # 标记最大目标位行
            if max_row >= 0:
                for col in range(1, self.table_widget.columnCount()):
                    item = self.table_widget.item(max_row, col)
                    if item is not None:
                        item.setBackground(color)
                print(f"单元格标记最大目标位行: {max_row}, 价格约: {max_target:.2f}")

            # 标记最小目标位行
            if min_row >= 0:
                for col in range(1, self.table_widget.columnCount()):
                    item = self.table_widget.item(min_row, col)
                    if item is not None:
                        item.setBackground(color)
                print(f"单元格标记最小目标位行: {min_row}, 价格约: {min_target:.2f}")

            # 在表头添加标记
            if max_row >= 0:
                header_item = self.table_widget.verticalHeaderItem(max_row)
                if header_item is not None:
                    # 在价格前添加箭头标记
                    current_text = header_item.text()
                    marker = "▲ " if is_upward else "▼ "
                    if not marker in current_text:
                        header_item.setText(marker + current_text)
                    header_item.setBackground(color)

            if min_row >= 0:
                header_item = self.table_widget.verticalHeaderItem(min_row)
                if header_item is not None:
                    # 在价格前添加箭头标记
                    current_text = header_item.text()
                    marker = "△ " if is_upward else "▽ "
                    if not marker in current_text:
                        header_item.setText(marker + current_text)
                    header_item.setBackground(color)

            # 强制刷新表格
            self.table_widget.update()

        except Exception as e:
            print(f"标记目标位单元格出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def keyPressEvent(self, event):
        """处理键盘事件"""
        print(f"[PointFigureChartWidget] 按键事件：{event.key()}, 修饰符：{event.modifiers()}")

        # 确保焦点在表格上
        if not self.table_widget.hasFocus():
            print("表格未获得焦点，强制获取焦点")
            self.table_widget.setFocus()

        # 处理快捷键 - 严格检查修饰键
        # Ctrl+Q 或 Ctrl+W：上行目标计算
        if event.modifiers() == Qt.ControlModifier and (event.key() == Qt.Key_Q or event.key() == Qt.Key_W):
            print("触发上行目标计算快捷键")
            # 先验证选择是否符合要求
            is_valid, error_message, _ = self._validate_selection_for_target_calculation()
            if is_valid:
                self._calculate_upward_target()
            else:
                QMessageBox.warning(self, "警告", error_message)
            event.accept()  # 阻止事件传播
            return
        # Ctrl+Z 或 Ctrl+X：下行目标计算
        elif event.modifiers() == Qt.ControlModifier and (event.key() == Qt.Key_Z or event.key() == Qt.Key_X):
            print("触发下行目标计算快捷键")
            # 先验证选择是否符合要求
            is_valid, error_message, _ = self._validate_selection_for_target_calculation()
            if is_valid:
                self._calculate_downward_target()
            else:
                QMessageBox.warning(self, "警告", error_message)
            event.accept()  # 阻止事件传播
            return

        # 如果不是我们处理的快捷键，传递给父类
        super().keyPressEvent(event)

    def eventFilter(self, watched, event):
        """事件过滤器，用于捕获表格的键盘事件和其他事件"""
        if event.type() == QEvent.KeyPress:
            print(f"[事件过滤器] 捕获键盘事件: {event.key()}, 修饰键: {event.modifiers()}")

            # 检查是否是目标计算快捷键 - 修改为使用简单的Ctrl组合键
            if event.modifiers() == Qt.ControlModifier:
                # 上行目标计算：Ctrl+W 或 Ctrl+Q
                if event.key() in [Qt.Key_W, Qt.Key_Q]:
                    print("事件过滤器触发上行目标计算")
                    # 先验证选择是否符合要求
                    is_valid, error_message, _ = self._validate_selection_for_target_calculation()
                    if is_valid:
                        self._calculate_upward_target()
                    else:
                        QMessageBox.warning(self, "警告", error_message)
                    return True
                # 下行目标计算：Ctrl+Z 或 Ctrl+X
                elif event.key() in [Qt.Key_Z, Qt.Key_X]:
                    print("事件过滤器触发下行目标计算")
                    # 先验证选择是否符合要求
                    is_valid, error_message, _ = self._validate_selection_for_target_calculation()
                    if is_valid:
                        self._calculate_downward_target()
                    else:
                        QMessageBox.warning(self, "警告", error_message)
                    return True

        # 处理表格右键菜单
        if (watched == self.table_widget and
            event.type() == QEvent.ContextMenu):
            # 确保鼠标右键点击在表格上
            self._show_context_menu(event.globalPos())
            return True

        # 处理表格大小变化事件
        if (watched == self.table_widget.viewport() and
            event.type() == QEvent.Resize):
            # 表格视口大小变化时更新目标叠加层的大小
            self._update_target_overlays_on_resize(event.size())
            # 更新成交量分布覆盖层位置
            self._update_volume_overlay_position()
            return False  # 继续处理事件

        # 处理表格滚动事件
        if (watched == self.table_widget or watched == self.table_widget.viewport()) and event.type() == QEvent.Scroll:
            # 更新成交量分布覆盖层位置
            self._update_volume_overlay_position()

        # 处理鼠标点击事件 - 为信息显示功能 (替换悬停逻辑)
        if (watched == self.table_widget.viewport() and
            event.type() == QEvent.MouseButtonPress):
            # 获取鼠标位置对应的单元格
            pos = event.pos()
            item = self.table_widget.itemAt(pos.x(), pos.y())
            if item and event.button() == Qt.LeftButton:  # 只响应左键点击
                row = item.row()
                col = item.column()
                # 记录当前点击单元格
                self.hover_cell = (row, col)
                # 显示信息
                self._show_cell_info()
            else:
                # 点击空白区域或者非左键点击
                self.hover_cell = None
                # 隐藏信息标签
                if self.hover_info_label and self.hover_info_label.isVisible():
                    self.hover_info_label.hide()

        # 处理鼠标移动事件 - 移除悬停信息显示逻辑
        if (watched == self.table_widget.viewport() and
            event.type() == QEvent.MouseMove):
            # 不再处理悬停显示信息
            pass

        # 处理鼠标离开事件
        if (watched == self.table_widget.viewport() and
            event.type() == QEvent.Leave):
            # 鼠标离开表格
            self.hover_cell = None
            # 隐藏信息标签
            if self.hover_info_label and self.hover_info_label.isVisible():
                self.hover_info_label.hide()

        # 未处理的事件交给父类处理
        return super().eventFilter(watched, event)

    def _update_target_overlays_on_resize(self, new_size):
        """更新目标叠加层的大小以适应表格视口大小变化"""
        if hasattr(self, 'target_overlays') and self.target_overlays:
            for overlay in self.target_overlays:
                # 更新叠加层的几何尺寸
                overlay.setGeometry(0, 0, new_size.width(), new_size.height())
                print(f"调整目标叠加层大小: {new_size.width()} x {new_size.height()}")

            # 使用定时器延迟重绘，确保尺寸变化后再绘制
            QTimer.singleShot(100, self._draw_overlays)
            QTimer.singleShot(150, self._draw_target_position_lines)

    def _update_volume_overlay_position(self):
        """当表格滚动或大小变化时更新成交量分布覆盖层位置"""
        if hasattr(self, 'volume_overlay') and self.volume_overlay:
            # 获取当前选区
            selected_ranges = self.table_widget.selectedRanges()
            if not selected_ranges:
                return

            # 计算选区在表格中的可见区域
            first_range = selected_ranges[0]
            first_cell = self.table_widget.item(first_range.topRow(), first_range.leftColumn())
            last_cell = self.table_widget.item(first_range.bottomRow(), first_range.rightColumn())

            if first_cell and last_cell:
                first_rect = self.table_widget.visualItemRect(first_cell)
                last_rect = self.table_widget.visualItemRect(last_cell)

                # 合并获得整个选区的矩形
                visible_rect = QRect(
                    first_rect.left(),
                    first_rect.top(),
                    last_rect.right() - first_rect.left(),
                    last_rect.bottom() - first_rect.top()
                )

                # 转换为表格视口坐标
                visible_rect.moveTo(
                    visible_rect.x() + self.table_widget.viewport().x(),
                    visible_rect.y() + self.table_widget.viewport().y()
                )

                # 更新覆盖层位置
                self.volume_overlay.setGeometry(visible_rect)

                # 更新覆盖层数据
                self.volume_overlay._map_prices_to_rows()

                # 重绘覆盖层
                self.volume_overlay.update()

    def _clear_target_overlays(self):
        """清除所有目标位线叠加层"""
        # 如果我们正在绘制新的目标线，允许清除
        # 否则，检查是否应该跳过清除以避免闪烁
        import time
        current_time = time.time()

        # 增强防闪烁保护：
        # 1. 如果正在绘制，不允许清除
        # 2. 如果距离上次绘制时间不够长（增加到30秒），跳过清除
        # 3. 如果是通过按钮明确要求清除，则允许清除

        # 判断是否是通过按钮明确请求的清除操作
        is_explicit_clear = hasattr(self, '_explicit_clear_requested') and self._explicit_clear_requested

        # 重置明确清除请求标志
        if is_explicit_clear:
            self._explicit_clear_requested = False

        # 防闪烁保护检查
        if not is_explicit_clear and (
            (hasattr(self, 'drawing_target_lines') and self.drawing_target_lines) or
            (hasattr(self, 'last_draw_time') and (current_time - self.last_draw_time < 30.0))  # 增加到30秒
        ):
            print(f"跳过清除目标线，防止闪烁 (距上次绘制 {current_time - self.last_draw_time:.2f}秒)")
            return

        if hasattr(self, 'target_overlays') and self.target_overlays:
            print("清除所有目标位线叠加层")
            for overlay in self.target_overlays:
                if overlay:
                    overlay.hide()
                    overlay.deleteLater()
            self.target_overlays = []

            # 恢复缩放功能
            self.zoom_disabled = False

            # 刷新表格视图
            if self.table_widget:
                self.table_widget.viewport().update()

            # 清理高亮状态
            # 恢复原始背景色
            for row in range(self.table_widget.rowCount()):
                header_item = self.table_widget.verticalHeaderItem(row)
                if header_item is not None:
                    header_item.setBackground(QBrush())  # 清除背景色

            # 清除选择区域信息
            if hasattr(self, 'selected_range'):
                delattr(self, 'selected_range')

            print("目标位线和高亮已清除")

    def _set_table_style(self):
        """设置表格样式"""
        # 设置主表格样式
        if self.table_widget:
            # 设置表格属性
            self.table_widget.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑
            self.table_widget.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 允许选择范围
            self.table_widget.setShowGrid(True)  # 显示网格线
            self.table_widget.setGridStyle(self.grid_style)  # 使用设置的网格线样式

            # 设置表格上下文菜单
            self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
            self.table_widget.customContextMenuRequested.connect(self._show_context_menu)

            # 设置表格样式表
            self.table_widget.setStyleSheet(f"""
                QTableWidget {{
                    background-color: {self.background_color.name()};
                    gridline-color: {self.grid_color.name()};
                }}
            """)

        # 设置成交量表格样式
        if hasattr(self, 'volume_table') and self.volume_table:
            self.volume_table.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑
            self.volume_table.setSelectionMode(QAbstractItemView.NoSelection)  # 不允许选择
            self.volume_table.setShowGrid(True)  # 显示网格线
            self.volume_table.setGridStyle(self.grid_style)  # 使用设置的网格线样式

            # 设置成交量表格样式表
            self.volume_table.setStyleSheet(f"""
                QTableWidget {{
                    background-color: {self.background_color.name()};
                    gridline-color: {self.grid_color.name()};
                }}
            """)

    def _show_cell_info(self):
        """显示单元格详细信息"""
        if not self.hover_cell:
            return

        row, col = self.hover_cell

        # 只处理数据列，不处理价格列
        if col == 0:
            return

        # 获取单元格信息
        info = self._get_cell_info(row, col)

        # 创建或更新信息标签
        if not self.hover_info_label:
            self.hover_info_label = QLabel(self)
            self.hover_info_label.setStyleSheet("""
                QLabel {
                    background-color: #FFFFCC;
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    padding: 5px;
                    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                }
            """)
            self.hover_info_label.setWordWrap(True)
            self.hover_info_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
            self.hover_info_label.setTextFormat(Qt.RichText)

        # 设置信息文本
        self.hover_info_label.setText(info)

        # 调整标签大小
        self.hover_info_label.adjustSize()

        # 计算显示位置
        pos = self.table_widget.viewport().mapToGlobal(self.table_widget.visualItemRect(self.table_widget.item(row, col)).bottomRight())
        pos = self.mapFromGlobal(pos)

        # 调整位置，确保不超出窗口边界
        width = self.hover_info_label.width()
        height = self.hover_info_label.height()

        if pos.x() + width > self.width():
            pos.setX(self.width() - width)
        if pos.y() + height > self.height():
            pos.setY(pos.y() - height - self.table_widget.rowHeight(row))

        self.hover_info_label.move(pos)
        self.hover_info_label.show()
        self.hover_info_label.raise_()

    def _get_cell_info(self, row, col):
        """获取单元格的详细信息"""
        try:
            # 获取格子类型（X或O）
            cell_item = self.table_widget.item(row, col)
            cell_type = cell_item.text() if cell_item else ""

            # 获取价格
            price_item = self.table_widget.item(row, 0)
            price = price_item.text() if price_item else "未知"

            # 获取列号
            col_header = self.table_widget.horizontalHeaderItem(col)
            col_num = col_header.text() if col_header else str(col)

            # 获取实际价格值（表格索引）
            actual_price = None
            if hasattr(self, 'chart_data') and self.chart_data is not None and not self.chart_data.empty:
                if row < len(self.chart_data.index):
                    actual_price = self.chart_data.index[row]

            # 获取日期和成交量信息
            date_info = ""
            volume_info = ""

            # 从DataFrame的attrs中获取额外信息
            if hasattr(self, 'chart_data') and self.chart_data is not None and hasattr(self.chart_data, 'attrs'):
                # 检查chart_data的属性中是否有日期和成交量信息
                if 'date_info' in self.chart_data.attrs and 'volume_info' in self.chart_data.attrs:
                    # 获取对应的日期和成交量DataFrame
                    date_df = self.chart_data.attrs['date_info']
                    volume_df = self.chart_data.attrs['volume_info']

                    # 实际的列索引是col-1（因为表格第0列是价格列）
                    chart_col = col - 1

                    # 检查日期信息
                    if chart_col >= 0 and chart_col < len(date_df.columns) and actual_price is not None:
                        # 尝试获取日期信息
                        try:
                            if actual_price in date_df.index and pd.notna(date_df.loc[actual_price, chart_col]):
                                date_info = date_df.loc[actual_price, chart_col]
                        except Exception:
                            pass

                    # 检查成交量信息
                    if chart_col >= 0 and chart_col < len(volume_df.columns) and actual_price is not None:
                        # 尝试获取成交量信息
                        try:
                            if actual_price in volume_df.index and pd.notna(volume_df.loc[actual_price, chart_col]):
                                volume_val = volume_df.loc[actual_price, chart_col]
                                # 格式化成交量
                                if isinstance(volume_val, (int, float)):
                                    volume_info = f"{int(volume_val):,}"
                                else:
                                    volume_info = str(volume_val)
                        except Exception:
                            pass

            # 构建信息文本 - 添加日期和成交量信息
            info_html = f"""
            <b>列号:</b> {col_num}<br/>
            <b>价格:</b> {price}
            """

            # 只有当单元格有XO标记时才显示日期和成交量
            if cell_type in ('X', 'O'):
                if date_info:
                    info_html += f"<br/><b>日期:</b> {date_info}"
                if volume_info:
                    info_html += f"<br/><b>成交量:</b> {volume_info}"
                info_html += f"<br/><b>标记:</b> {cell_type}"
            else:
                # 没有XO标记的单元格只显示基本信息
                info_html += f"<br/><b>标记:</b> {cell_type}"

            return info_html

        except Exception as e:
            print(f"获取单元格信息出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return "获取信息出错"

    def _explicit_clear_target_overlays(self):
        """明确请求清除目标位线"""
        # 设置明确清除请求标志
        self._explicit_clear_requested = True
        # 直接恢复缩放功能
        self.zoom_disabled = False
        # 调用清除方法
        self._clear_target_overlays()

    def _toggle_auto_box_size(self, state):
        """切换自动格值计算"""
        if state == Qt.Checked:
            self.auto_box_size = True
            self.box_size_spin.setValue(0.1)  # 设置为特殊值，显示为空白
            self.box_size_spin.setEnabled(False)
        else:
            self.auto_box_size = False
            self.box_size_spin.setValue(self.box_size)
            self.box_size_spin.setEnabled(True)

    def _save_as_default_settings(self):
        """将当前所有设置保存为默认值"""
        try:
            # 将当前设置保存到chart_controller的settings对象中

            # 图表参数
            self.chart_controller.settings.set_setting("chart", "box_size", self.box_size)
            self.chart_controller.settings.set_setting("chart", "reversal_amount", self.reversal_amount)

            # 将内部的method名称转换为设置值
            method_value = "high_low" if self.method == "高低价法" else "close"
            self.chart_controller.settings.set_setting("chart", "method", method_value)

            # 自动格值设置
            self.chart_controller.settings.set_setting("chart", "auto_box_size", self.auto_box_size)

            # 成交量设置
            self.chart_controller.settings.set_setting("chart", "volume_scale", self.volume_scale)

            # UI设置 - 更新键名以匹配settings.json
            # 颜色设置
            self.chart_controller.settings.set_setting("ui", "background_color", self.background_color.name())
            self.chart_controller.settings.set_setting("ui", "chart_bg_color", self.background_color.name())  # 添加chart_bg_color
            self.chart_controller.settings.set_setting("ui", "chart_grid_color", self.grid_color.name())
            self.chart_controller.settings.set_setting("ui", "chart_x_color", self.x_symbol_color.name())
            self.chart_controller.settings.set_setting("ui", "chart_o_color", self.o_symbol_color.name())
            self.chart_controller.settings.set_setting("ui", "x_symbol_color", self.x_symbol_color.name())  # 保留原键名
            self.chart_controller.settings.set_setting("ui", "o_symbol_color", self.o_symbol_color.name())  # 保留原键名

            # 成交量颜色设置
            self.chart_controller.settings.set_setting("ui", "volume_up_color", self.volume_up_color.name())
            self.chart_controller.settings.set_setting("ui", "volume_down_color", self.volume_down_color.name())
            self.chart_controller.settings.set_setting("ui", "volume_increase_color", self.volume_up_color.name())  # 添加volume_increase_color
            self.chart_controller.settings.set_setting("ui", "volume_decrease_color", self.volume_down_color.name())  # 添加volume_decrease_color

            # 价格列颜色设置
            self.chart_controller.settings.set_setting("ui", "price_bg_color", self.price_bg_color.name())  # 添加价格列背景色
            self.chart_controller.settings.set_setting("ui", "price_text_color", self.price_text_color.name())  # 添加价格列文本色

            # 网格样式 - 保存数字索引和文本表示
            grid_style_index = self.grid_style_combo.currentIndex()
            self.chart_controller.settings.set_setting("ui", "grid_style", grid_style_index)

            # 添加网格样式的文本表示
            grid_style_text = ["实线", "虚线", "点线"][grid_style_index] if grid_style_index < 3 else "实线"
            self.chart_controller.settings.set_setting("ui", "chart_grid_style", grid_style_text)

            # 成交量显示设置
            self.chart_controller.settings.set_setting("ui", "show_volume", self.show_volume_chart)
            self.chart_controller.settings.set_setting("ui", "show_volume_chart", self.show_volume_chart)  # 添加show_volume_chart

            # 添加默认字体大小设置
            self.chart_controller.settings.set_setting("ui", "font_size", 12)

            # 保存设置到文件
            self.chart_controller.settings.save_settings()

            # 获取设置文件路径
            settings_file_path = self.chart_controller.settings.settings_file

            # 显示成功消息，包含设置文件路径
            QMessageBox.information(
                self,
                "设置保存成功",
                f"当前设置已保存为默认值\n\n保存位置: {settings_file_path}"
            )

        except Exception as e:
            QMessageBox.critical(self, "保存默认设置出错", f"无法保存默认设置: {str(e)}")

    def _refresh_chart(self):
        """将图表重置到初始状态"""
        try:
            # 清除所有叠加层
            self._clear_target_overlays()

            # 清除支撑/阻力位
            self.support_resistance_levels = []

            # 清除趋势线
            self.trendlines = []

            # 取消所有选择
            if self.table_widget:
                self.table_widget.clearSelection()

            # 重新生成点数图
            self.update_chart()

            # 显示成功消息
            self.status_bar.setText("图表已刷新")

        except Exception as e:
            QMessageBox.critical(self, "刷新图表出错", f"无法刷新图表: {str(e)}")

    def _calculate_box_size(self, price):
        """根据价格自动计算推荐的格值

        参数:
            price (float): 最新价格或价格范围的中位数

        返回:
            float: 推荐的格值
        """
        # 根据图片中的参考表计算格值
        if price < 0.25:
            return 0.0625
        elif 0.25 <= price < 1:
            return 0.125
        elif 1 <= price < 5:
            return 0.25
        elif 5 <= price < 20:
            return 0.5
        elif 20 <= price < 100:
            return 1
        elif 100 <= price < 200:
            return 2
        elif 200 <= price < 500:
            return 4
        elif 500 <= price < 1000:
            return 5
        elif 1000 <= price < 25000:
            return 50
        else:  # price >= 25000
            return 500

    def _on_box_size_changed(self, value):
        """格值输入框值变更处理"""
        # 如果用户手动修改了格值（当前值不是特殊值0.1），则禁用自动计算
        if self.auto_box_size and value != 0.1:
            self.auto_box_size = False
            self.auto_box_size_check.setChecked(False)
            self.box_size_spin.setEnabled(True)

    def _save_splitter_state(self, pos=None, index=None):
        """保存分割器状态到设置中"""
        # 获取当前分割器大小
        sizes = self.splitter.sizes()

        # 只有当成交量子图可见时才保存分割器状态
        if self.show_volume_chart and len(sizes) == 2 and sizes[1] > 0:
            # 保存到设置
            self.chart_controller.settings.set_setting("ui", "splitter_sizes", sizes)
            # 立即保存设置
            self.chart_controller.settings.save_settings()

    def _reset_splitter(self):
        """重置分割器到默认状态"""
        if self.show_volume_chart:
            # 记录当前总高度
            total_height = sum(self.splitter.sizes())
            # 根据3:1的比例计算新的大小
            new_sizes = [int(total_height * 0.75), int(total_height * 0.25)]
            self.splitter.setSizes(new_sizes)

            # 保存状态
            self._save_splitter_state()

            # 提示用户
            if hasattr(self.parent(), 'statusBar'):
                self.parent().statusBar().showMessage("成交量子图比例已重置为默认值", 3000)

    def generate_analysis_report(self):
        """生成分析报告"""
        # 显示功能正在开发中的消息
        QMessageBox.information(self, "提示", "分析报告功能正在开发中")

    def copy_chart_to_clipboard(self):
        """复制图表到剪贴板"""
        # 显示功能正在开发中的消息
        QMessageBox.information(self, "提示", "复制图表功能正在开发中")

    def export_chart(self):
        """导出图表"""
        # 显示功能正在开发中的消息
        QMessageBox.information(self, "提示", "导出图表功能正在开发中")

    def _show_volume_distribution(self):
        """显示选中区域的成交量分布图"""
        selected_ranges = self.table_widget.selectedRanges()
        if not selected_ranges:
            return

        try:
            # 确保chart_data有效
            if not hasattr(self, 'chart_data') or self.chart_data is None or self.chart_data.empty:
                QMessageBox.warning(self, "警告", "没有数据可供分析")
                return

            # 确保chart_data.attrs中有volume_info
            if not hasattr(self.chart_data, 'attrs') or 'volume_info' not in self.chart_data.attrs:
                QMessageBox.warning(self, "警告", "数据中没有成交量信息")
                return

            # 获取成交量数据
            volume_df = self.chart_data.attrs['volume_info']

            # 创建一个字典来存储每个价格的成交量总和
            volume_by_price = {}
            # 创建一个字典来存储每个价格对应的符号类型(X或O)
            symbol_by_price = {}

            # 处理所有的选择区域
            for selected_range in selected_ranges:
                # 获取区域的行列范围
                top_row = selected_range.topRow()
                bottom_row = selected_range.bottomRow()
                left_col = selected_range.leftColumn()
                right_col = selected_range.rightColumn()

                # 跳过价格列（列索引为0）
                if left_col == 0:
                    left_col = 1

                # 遍历选中的单元格
                for row in range(top_row, bottom_row + 1):
                    # 获取价格（从表格索引）
                    if row >= len(self.chart_data.index):
                        continue

                    price = self.chart_data.index[row]

                    # 遍历选中的列
                    for col in range(left_col, right_col + 1):
                        # 实际列索引（减去价格列）
                        chart_col = col - 1

                        if chart_col >= len(volume_df.columns):
                            continue

                        # 获取该单元格是否有X或O符号
                        symbol = None
                        if chart_col in self.chart_data.columns and pd.notna(self.chart_data.iloc[row, chart_col]):
                            symbol = self.chart_data.iloc[row, chart_col]

                        # 如果有符号，获取该单元格的成交量
                        if symbol in ('X', 'O'):
                            try:
                                if price in volume_df.index and pd.notna(volume_df.loc[price, chart_col]):
                                    volume = volume_df.loc[price, chart_col]

                                    # 初始化或累加成交量
                                    if price not in volume_by_price:
                                        volume_by_price[price] = 0
                                        # 记录当前价格对应的符号类型
                                        symbol_by_price[price] = symbol

                                    volume_by_price[price] += float(volume)
                            except Exception as e:
                                print(f"获取成交量出错: {e}")

            # 如果没有找到有效数据，显示警告
            if not volume_by_price:
                QMessageBox.warning(self, "警告", "选中区域内没有找到有效的成交量数据")
                return

            # 计算选区在表格中的可见区域
            visible_rect = None
            if selected_ranges:
                first_range = selected_ranges[0]
                # 获取选区的第一个和最后一个单元格的矩形
                first_cell = self.table_widget.item(first_range.topRow(), first_range.leftColumn())
                last_cell = self.table_widget.item(first_range.bottomRow(), first_range.rightColumn())

                if first_cell and last_cell:
                    first_rect = self.table_widget.visualItemRect(first_cell)
                    last_rect = self.table_widget.visualItemRect(last_cell)

                    # 合并获得整个选区的矩形
                    visible_rect = QRect(
                        first_rect.left(),
                        first_rect.top(),
                        last_rect.right() - first_rect.left(),
                        last_rect.bottom() - first_rect.top()
                    )

                    # 转换为表格视口坐标
                    visible_rect.moveTo(
                        visible_rect.x() + self.table_widget.viewport().x(),
                        visible_rect.y() + self.table_widget.viewport().y()
                    )

            # 如果无法获取可见矩形，使用选区大小的默认矩形
            if not visible_rect:
                # 使用表格视口的中心位置创建一个默认矩形
                viewport_rect = self.table_widget.viewport().rect()
                visible_rect = QRect(
                    viewport_rect.center().x() - 200,
                    viewport_rect.center().y() - 150,
                    400,
                    300
                )

            # 如果已经有覆盖层，先关闭它
            if hasattr(self, 'volume_overlay') and self.volume_overlay:
                self.volume_overlay.close_overlay()
                self.volume_overlay = None

            # 创建并显示成交量分布覆盖层
            self.volume_overlay = VolumeDistributionOverlay(
                self.table_widget,
                volume_by_price,
                symbol_by_price,
                visible_rect,
                is_simplified=True  # 使用简化模式，隐藏标题和总成交量信息
            )

            # 连接关闭信号
            self.volume_overlay.closed.connect(self._on_volume_overlay_closed)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成成交量分布图出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _on_volume_overlay_closed(self):
        """处理成交量分布覆盖层关闭事件"""
        self.volume_overlay = None
        # 刷新表格显示
        self.table_widget.update()

    def _validate_selection_for_volume_distribution(self):
        """
        验证选区是否满足显示成交量分布图的要求

        要求：
        - 至少选择两行
        - 每行至少包含两格

        返回:
            tuple: (是否有效, 错误消息)
        """
        # 获取选择区域
        selected_ranges = self.table_widget.selectedRanges()

        # 检查是否有选择区域
        if not selected_ranges:
            return False, "请先选择表格区域"

        # 获取第一个选择区域
        selected_range = selected_ranges[0]

        # 获取行数和列数
        row_count = selected_range.bottomRow() - selected_range.topRow() + 1
        col_count = selected_range.rightColumn() - selected_range.leftColumn() + 1

        # 如果只选择了价格列（索引为0的列），实际有效列数为0
        if selected_range.leftColumn() == 0 and col_count == 1:
            return False, "请包含价格列以外的数据列"

        # 调整有效列数（如果包含价格列，则有效列数减1）
        effective_col_count = col_count
        if selected_range.leftColumn() == 0:
            effective_col_count -= 1

        # 检查是否至少选择了两行
        if row_count < 2:
            return False, "请至少选择两行"

        # 检查是否每行至少有两格
        if effective_col_count < 2:
            return False, "请每行至少选择两格数据"

        return True, ""

    def _on_distribution_button_clicked(self):
        """
        处理分布图按钮点击事件
        """
        # 验证选区
        is_valid, error_message = self._validate_selection_for_volume_distribution()

        # 如果选区无效，显示错误消息
        if not is_valid:
            QMessageBox.warning(self, "选区无效", error_message)
            return

        # 显示成交量分布图 - 工具栏按钮使用覆盖层方式显示
        self._show_volume_distribution_overlay()

    def _show_volume_distribution_overlay(self):
        """直接在图表上显示选中区域的成交量分布图（覆盖层方式）"""
        selected_ranges = self.table_widget.selectedRanges()
        if not selected_ranges:
            return

        try:
            # 确保chart_data有效
            if not hasattr(self, 'chart_data') or self.chart_data is None or self.chart_data.empty:
                QMessageBox.warning(self, "警告", "没有数据可供分析")
                return

            # 确保chart_data.attrs中有volume_info
            if not hasattr(self.chart_data, 'attrs') or 'volume_info' not in self.chart_data.attrs:
                QMessageBox.warning(self, "警告", "数据中没有成交量信息")
                return

            # 获取成交量数据
            volume_df = self.chart_data.attrs['volume_info']

            # 创建一个字典来存储每个价格的成交量总和
            volume_by_price = {}
            # 创建一个字典来存储每个价格对应的符号类型(X或O)
            symbol_by_price = {}

            # 处理所有的选择区域
            for selected_range in selected_ranges:
                # 获取区域的行列范围
                top_row = selected_range.topRow()
                bottom_row = selected_range.bottomRow()
                left_col = selected_range.leftColumn()
                right_col = selected_range.rightColumn()

                # 跳过价格列（列索引为0）
                if left_col == 0:
                    left_col = 1

                # 遍历选中的单元格
                for row in range(top_row, bottom_row + 1):
                    # 获取价格（从表格索引）
                    if row >= len(self.chart_data.index):
                        continue

                    price = self.chart_data.index[row]

                    # 遍历选中的列
                    for col in range(left_col, right_col + 1):
                        # 实际列索引（减去价格列）
                        chart_col = col - 1

                        if chart_col >= len(volume_df.columns):
                            continue

                        # 获取该单元格是否有X或O符号
                        symbol = None
                        if chart_col in self.chart_data.columns and pd.notna(self.chart_data.iloc[row, chart_col]):
                            symbol = self.chart_data.iloc[row, chart_col]

                        # 如果有符号，获取该单元格的成交量
                        if symbol in ('X', 'O'):
                            try:
                                if price in volume_df.index and pd.notna(volume_df.loc[price, chart_col]):
                                    volume = volume_df.loc[price, chart_col]

                                    # 初始化或累加成交量
                                    if price not in volume_by_price:
                                        volume_by_price[price] = 0
                                        # 记录当前价格对应的符号类型
                                        symbol_by_price[price] = symbol

                                    volume_by_price[price] += float(volume)
                            except Exception as e:
                                print(f"获取成交量出错: {e}")

            # 如果没有找到有效数据，显示警告
            if not volume_by_price:
                QMessageBox.warning(self, "警告", "选中区域内没有找到有效的成交量数据")
                return

            # 计算选区在表格中的可见区域
            visible_rect = None
            if selected_ranges:
                first_range = selected_ranges[0]
                # 获取选区的第一个和最后一个单元格的矩形
                first_cell = self.table_widget.item(first_range.topRow(), first_range.leftColumn())
                last_cell = self.table_widget.item(first_range.bottomRow(), first_range.rightColumn())

                if first_cell and last_cell:
                    first_rect = self.table_widget.visualItemRect(first_cell)
                    last_rect = self.table_widget.visualItemRect(last_cell)

                    # 合并获得整个选区的矩形
                    visible_rect = QRect(
                        first_rect.left(),
                        first_rect.top(),
                        last_rect.right() - first_rect.left(),
                        last_rect.bottom() - first_rect.top()
                    )

                    # 转换为表格视口坐标
                    visible_rect.moveTo(
                        visible_rect.x() + self.table_widget.viewport().x(),
                        visible_rect.y() + self.table_widget.viewport().y()
                    )

            # 如果无法获取可见矩形，使用选区大小的默认矩形
            if not visible_rect:
                # 使用表格视口的中心位置创建一个默认矩形
                viewport_rect = self.table_widget.viewport().rect()
                visible_rect = QRect(
                    viewport_rect.center().x() - 200,
                    viewport_rect.center().y() - 150,
                    400,
                    300
                )

            # 如果已经有覆盖层，先关闭它
            if hasattr(self, 'volume_overlay') and self.volume_overlay:
                self.volume_overlay.close_overlay()
                self.volume_overlay = None

            # 创建并显示成交量分布覆盖层
            self.volume_overlay = VolumeDistributionOverlay(
                self.table_widget,
                volume_by_price,
                symbol_by_price,
                visible_rect,
                is_simplified=True  # 使用简化模式，隐藏标题和总成交量信息
            )

            # 连接关闭信号
            self.volume_overlay.closed.connect(self._on_volume_overlay_closed)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成成交量分布图出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _show_volume_distribution_dialog(self):
        """显示成交量分布图对话框（弹窗方式）"""
        try:
            # 验证选区
            is_valid, error_message = self._validate_selection_for_volume_distribution()

            # 如果选区无效，显示错误消息
            if not is_valid:
                QMessageBox.warning(self, "选区无效", error_message)
                return

            # 以下代码与_show_volume_distribution相同，但最后使用对话框显示
            selected_ranges = self.table_widget.selectedRanges()

            # 确保chart_data有效
            if not hasattr(self, 'chart_data') or self.chart_data is None or self.chart_data.empty:
                QMessageBox.warning(self, "警告", "没有数据可供分析")
                return

            # 确保chart_data.attrs中有volume_info
            if not hasattr(self.chart_data, 'attrs') or 'volume_info' not in self.chart_data.attrs:
                QMessageBox.warning(self, "警告", "数据中没有成交量信息")
                return

            # 获取成交量数据
            volume_df = self.chart_data.attrs['volume_info']

            # 创建一个字典来存储每个价格的成交量总和
            volume_by_price = {}
            # 创建一个字典来存储每个价格对应的符号类型(X或O)
            symbol_by_price = {}

            # 处理所有的选择区域
            for selected_range in selected_ranges:
                # 获取区域的行列范围
                top_row = selected_range.topRow()
                bottom_row = selected_range.bottomRow()
                left_col = selected_range.leftColumn()
                right_col = selected_range.rightColumn()

                # 跳过价格列（列索引为0）
                if left_col == 0:
                    left_col = 1

                # 遍历选中的单元格
                for row in range(top_row, bottom_row + 1):
                    # 获取价格（从表格索引）
                    if row >= len(self.chart_data.index):
                        continue

                    price = self.chart_data.index[row]

                    # 遍历选中的列
                    for col in range(left_col, right_col + 1):
                        # 实际列索引（减去价格列）
                        chart_col = col - 1

                        if chart_col >= len(volume_df.columns):
                            continue

                        # 获取该单元格是否有X或O符号
                        symbol = None
                        if chart_col in self.chart_data.columns and pd.notna(self.chart_data.iloc[row, chart_col]):
                            symbol = self.chart_data.iloc[row, chart_col]

                        # 如果有符号，获取该单元格的成交量
                        if symbol in ('X', 'O'):
                            try:
                                if price in volume_df.index and pd.notna(volume_df.loc[price, chart_col]):
                                    volume = volume_df.loc[price, chart_col]

                                    # 初始化或累加成交量
                                    if price not in volume_by_price:
                                        volume_by_price[price] = 0
                                        # 记录当前价格对应的符号类型
                                        symbol_by_price[price] = symbol

                                    volume_by_price[price] += float(volume)
                            except Exception as e:
                                print(f"获取成交量出错: {e}")

            # 如果没有找到有效数据，显示警告
            if not volume_by_price:
                QMessageBox.warning(self, "警告", "选中区域内没有找到有效的成交量数据")
                return

            # 创建并显示成交量分布对话框
            dialog = VolumeDistributionDialog(volume_by_price, symbol_by_price, self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成成交量分布图出错: {str(e)}")
            import traceback
            traceback.print_exc()


class TargetOverlay(QWidget):
    """目标价位叠加层，用于绘制目标线和标记"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)  # 鼠标事件穿透
        self.setAttribute(Qt.WA_NoSystemBackground)        # 无系统背景
        self.setAttribute(Qt.WA_TranslucentBackground)     # 透明背景

        # 绘制参数
        self.start_x = 0
        self.start_y = 0
        self.min_x = 0
        self.min_y = 0
        self.max_x = 0
        self.max_y = 0
        self.min_price = 0
        self.max_price = 0
        self.is_upward = True
        self.has_points = False
        self.parent_table = None

        # 添加滚动信息
        self.h_scroll = 0
        self.v_scroll = 0
        self.last_h_scroll = 0
        self.last_v_scroll = 0

        # 记录父表格
        if parent and isinstance(parent, QTableWidget):
            self.parent_table = parent
            # 连接滚动信号
            if hasattr(parent, 'horizontalScrollBar') and hasattr(parent, 'verticalScrollBar'):
                parent.horizontalScrollBar().valueChanged.connect(self.update_scroll_position)
                parent.verticalScrollBar().valueChanged.connect(self.update_scroll_position)

    def set_points(self, start_x, start_y, min_x, min_y, max_x, max_y, min_price, max_price, is_upward):
        """设置绘制的点坐标和目标价格"""
        self.start_x = start_x
        self.start_y = start_y
        self.min_x = min_x
        self.min_y = min_y
        self.max_x = max_x
        self.max_y = max_y
        self.min_price = min_price
        self.max_price = max_price
        self.is_upward = is_upward
        self.has_points = True
        self.update()  # 触发重绘

    def set_scroll_info(self, h_scroll, v_scroll):
        """设置表格滚动信息"""
        self.h_scroll = h_scroll
        self.v_scroll = v_scroll
        self.last_h_scroll = h_scroll
        self.last_v_scroll = v_scroll
        print(f"设置初始滚动位置: 水平={h_scroll}, 垂直={v_scroll}")

    def update_scroll_position(self, value):
        """更新滚动位置并重绘"""
        if not self.parent_table:
            return

        # 获取最新滚动位置
        h_scroll = self.parent_table.horizontalScrollBar().value()
        v_scroll = self.parent_table.verticalScrollBar().value()

        # 检查是否有变化
        if h_scroll != self.last_h_scroll or v_scroll != self.last_v_scroll:
            # 计算滚动偏移量
            h_delta = h_scroll - self.last_h_scroll
            v_delta = v_scroll - self.last_v_scroll

            print(f"表格滚动更新: 水平={h_scroll}(偏移{h_delta}), 垂直={v_scroll}(偏移{v_delta})")

            # 更新记录的滚动位置
            self.last_h_scroll = h_scroll
            self.last_v_scroll = v_scroll

            # 更新当前的滚动位置
            self.h_scroll = h_scroll
            self.v_scroll = v_scroll

            # 触发重绘
            self.update()

    def _find_suitable_marker_position(self, row, base_x, is_min=True):
        """寻找合适的标记放置位置

        Args:
            row: 目标位所在行
            base_x: 基准X坐标
            is_min: 是否为最小目标位

        Returns:
            合适的X坐标位置
        """
        if not self.parent_table:
            return base_x + 40  # 默认偏移

        try:
            # 记录原始行，用于日志
            orig_row = row

            # 从表格中确定实际行
            row = -1
            if is_min:
                # 通过y坐标确定最小目标位行
                for r in range(self.parent_table.rowCount()):
                    item = self.parent_table.item(r, 0)
                    if item:
                        rect = self.parent_table.visualItemRect(item)
                        if abs(rect.center().y() - self.min_y) < 10:  # 10px误差范围内
                            row = r
                            break
            else:
                # 通过y坐标确定最大目标位行
                for r in range(self.parent_table.rowCount()):
                    item = self.parent_table.item(r, 0)
                    if item:
                        rect = self.parent_table.visualItemRect(item)
                        if abs(rect.center().y() - self.max_y) < 10:  # 10px误差范围内
                            row = r
                            break

            # 如果找不到行，使用默认偏移
            if row < 0:
                print(f"无法确定行号 y={self.min_y if is_min else self.max_y}, 使用默认偏移")
                return base_x + 40

            print(f"寻找标记位置: 原始行={orig_row}, 实际行={row}, 基准x={base_x}, 类型={'最小' if is_min else '最大'}")

            # 找到起始点右侧位置最近的列
            start_col = -1
            min_distance = float('inf')
            for col in range(1, self.parent_table.columnCount()):  # 跳过价格列(0)
                item = self.parent_table.item(0, col)  # 使用首行确定列位置
                if item:
                    rect = self.parent_table.visualItemRect(item)
                    # 找出距离基准点最近的右侧列
                    if rect.x() > base_x and rect.x() - base_x < min_distance:
                        min_distance = rect.x() - base_x
                        start_col = col

            # 如果找不到右侧的列，尝试采用基准点偏移一段距离
            if start_col < 0:
                return base_x + 40  # 默认偏移

            # 从找到的最近列开始，向右查找不含X或O的位置
            for offset in range(0, 10):  # 尝试向右10列
                check_col = start_col + offset
                if check_col >= self.parent_table.columnCount():
                    break  # 超出表格范围

                # 检查该位置是否有X或O
                cell_item = self.parent_table.item(row, check_col)
                if not cell_item or cell_item.text() not in ('X', 'O'):
                    # 找到空白位置
                    marker_item = self.parent_table.item(row, check_col) or self.parent_table.item(row, start_col)
                    if marker_item:
                        rect = self.parent_table.visualItemRect(marker_item)
                        suitable_x = rect.center().x()
                        print(f"找到合适的右侧空白位置: 行={row}, 列={check_col}, x={suitable_x}")
                        return suitable_x

            # 如果右侧没找到空位，尝试向左查找（从第1列开始，避开价格列）
            for col in range(1, self.parent_table.columnCount()):
                # 检查该位置是否有X或O
                cell_item = self.parent_table.item(row, col)
                if not cell_item or cell_item.text() not in ('X', 'O'):
                    # 找到空白位置
                    rect = self.parent_table.visualItemRect(self.parent_table.item(row, col) or self.parent_table.item(row, 1))
                    suitable_x = rect.center().x()
                    print(f"找到合适的左侧空白位置: 行={row}, 列={col}, x={suitable_x}")
                    return suitable_x

            # 如果仍然没找到合适位置，使用起始点右侧一段距离的位置
            print(f"未找到不含X或O的位置，使用默认偏移")
            return base_x + 60  # 使用较大偏移，避免覆盖

        except Exception as e:
            print(f"寻找标记位置出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return base_x + 60  # 出错时使用较大默认偏移

    def paintEvent(self, event):
        """绘制事件处理"""
        if not self.has_points:
            return

        # 创建绘制器
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿

        # 设置颜色
        line_color = QColor(255, 0, 255) if self.is_upward else QColor(0, 180, 0)  # 调整为更亮的绿色

        # 设置线条样式
        pen = QPen(line_color, 2, Qt.SolidLine)

        # 计算滚动位置调整值 - 由于我们现在在视口上绘制，不需要额外调整滚动偏移
        # 因为视口坐标系就是相对于当前可见区域的，会自动考虑滚动的影响

        # 打印调试信息
        print(f"绘制目标位线 - 滚动位置: 水平={self.h_scroll}, 垂直={self.v_scroll}")
        print(f"绘制坐标: 起点({self.start_x},{self.start_y}) -> 最小目标({self.min_x},{self.min_y}), 最大目标({self.max_x},{self.max_y})")

        # 绘制垂直连接线(从选择点到水平线高度)
        painter.setPen(pen)
        # 使用水平线高度中点作为连接点，确保转换为整数
        horizontal_y = int((self.max_y + self.min_y) / 2)
        painter.drawLine(int(self.start_x), int(self.start_y), int(self.start_x), horizontal_y)

        # 智能寻找标记位置
        max_marker_x = self._find_suitable_marker_position(int(self.max_y), int(self.start_x), False)
        min_marker_x = self._find_suitable_marker_position(int(self.min_y), int(self.start_x), True)

        # 绘制水平线段 - 实线(到最大目标) - 修改为从起始点x坐标开始，而不是从价格列开始
        painter.drawLine(int(self.start_x), int(self.max_y), max_marker_x, int(self.max_y))

        # 绘制水平线段 - 虚线(到最小目标) - 修改为从起始点x坐标开始，而不是从价格列开始
        pen.setStyle(Qt.DashLine)
        painter.setPen(pen)
        painter.drawLine(int(self.start_x), int(self.min_y), min_marker_x, int(self.min_y))

        # 绘制最大目标位标记
        painter.setBrush(line_color)
        pen.setStyle(Qt.SolidLine)
        painter.setPen(pen)
        painter.drawRect(max_marker_x - 5, int(self.max_y) - 5, 10, 10)  # 方块标记

        # 绘制最小目标位标记
        pen.setStyle(Qt.DashLine)
        painter.setPen(pen)
        painter.setBrush(line_color)
        painter.drawEllipse(min_marker_x - 5, int(self.min_y) - 5, 10, 10)  # 圆形标记

        # 绘制价格标签
        font = painter.font()
        font.setBold(True)
        painter.setFont(font)

        # 绘制最大目标价格标签
        max_label = f"{self.max_price:.2f}"
        painter.setPen(QColor(0, 0, 0))  # 黑色文本
        # 绘制白色背景
        text_rect = painter.fontMetrics().boundingRect(max_label)
        bg_rect = QRect(max_marker_x + 8, int(self.max_y) - 12, text_rect.width() + 4, text_rect.height() + 2)
        painter.fillRect(bg_rect, QColor(255, 255, 255, 200))  # 半透明白色背景
        painter.drawText(max_marker_x + 10, int(self.max_y) + 4, max_label)

        # 绘制最小目标价格标签
        min_label = f"{self.min_price:.2f}"
        # 绘制白色背景
        text_rect = painter.fontMetrics().boundingRect(min_label)
        bg_rect = QRect(min_marker_x + 8, int(self.min_y) - 12, text_rect.width() + 4, text_rect.height() + 2)
        painter.fillRect(bg_rect, QColor(255, 255, 255, 200))  # 半透明白色背景
        painter.drawText(min_marker_x + 10, int(self.min_y) + 4, min_label)


class VolumeDistributionDialog(QDialog):
    """成交量分布图对话框"""

    def __init__(self, volume_data, symbol_data=None, parent=None):
        """
        初始化成交量分布图对话框

        参数:
            volume_data (dict): 成交量数据，格式为 {价格: 成交量}
            symbol_data (dict, optional): 符号数据，格式为 {价格: 符号}，用于区分X和O
            parent (QWidget): 父窗口
        """
        super().__init__(parent)
        self.volume_data = volume_data
        self.symbol_data = symbol_data or {}  # 如果未提供，则使用空字典
        self.setWindowTitle("成交量分布图")
        self.setMinimumSize(600, 400)

        # 设置窗口图标
        icon = QIcon()
        icon.addPixmap(QPixmap(":/icons/chart.png"), QIcon.Normal, QIcon.Off)
        self.setWindowIcon(icon)

        # 初始化UI
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建一个水平布局来放置图表和图例
        chart_layout = QHBoxLayout()

        # 创建图表控件
        self.figure = plt.figure(figsize=(8, 5), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)

        # 创建图表类型选择区域
        chart_type_group = QGroupBox("图表类型")
        chart_type_layout = QVBoxLayout(chart_type_group)

        self.bar_chart_radio = QRadioButton("柱状图")
        self.pie_chart_radio = QRadioButton("饼图")
        self.bar_chart_radio.setChecked(True)

        chart_type_layout.addWidget(self.bar_chart_radio)
        chart_type_layout.addWidget(self.pie_chart_radio)

        # 创建设置区域
        settings_group = QGroupBox("设置")
        settings_layout = QVBoxLayout(settings_group)

        # 排序方式
        sort_layout = QHBoxLayout()
        sort_layout.addWidget(QLabel("排序方式:"))
        self.sort_combo = QComboBox()
        self.sort_combo.addItem("按价格升序")
        self.sort_combo.addItem("按价格降序")
        self.sort_combo.addItem("按成交量升序")
        self.sort_combo.addItem("按成交量降序")
        sort_layout.addWidget(self.sort_combo)
        settings_layout.addLayout(sort_layout)

        # 显示百分比选择
        self.show_percentage_check = QCheckBox("显示百分比")
        self.show_percentage_check.setChecked(True)
        settings_layout.addWidget(self.show_percentage_check)

        # 组合显示和设置区域
        control_layout = QVBoxLayout()
        control_layout.addWidget(chart_type_group)
        control_layout.addWidget(settings_group)
        control_layout.addStretch(1)

        # 添加到主布局
        chart_layout.addWidget(self.canvas, 4)
        chart_layout.addLayout(control_layout, 1)

        main_layout.addWidget(self.toolbar)
        main_layout.addLayout(chart_layout)

        # 添加按钮布局
        button_layout = QHBoxLayout()
        refresh_button = QPushButton("刷新图表")
        export_button = QPushButton("导出图表")
        close_button = QPushButton("关闭")

        button_layout.addWidget(refresh_button)
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        main_layout.addLayout(button_layout)

        # 连接信号
        self.bar_chart_radio.toggled.connect(self._plot_chart)
        self.pie_chart_radio.toggled.connect(self._plot_chart)
        self.sort_combo.currentIndexChanged.connect(self._plot_chart)
        self.show_percentage_check.toggled.connect(self._plot_chart)
        refresh_button.clicked.connect(self._plot_chart)
        export_button.clicked.connect(self._export_chart)
        close_button.clicked.connect(self.close)

        # 绘制图表
        self._plot_chart()

    def _plot_chart(self):
        """根据当前设置绘制图表"""
        # 清除图表
        self.figure.clear()

        # 创建子图
        ax = self.figure.add_subplot(111)

        # 按照选择的顺序排序数据
        data = self.volume_data.copy()
        sort_option = self.sort_combo.currentIndex()

        if sort_option == 0:  # 按价格升序
            sorted_data = sorted(data.items(), key=lambda x: x[0])
        elif sort_option == 1:  # 按价格降序
            sorted_data = sorted(data.items(), key=lambda x: x[0], reverse=True)
        elif sort_option == 2:  # 按成交量升序
            sorted_data = sorted(data.items(), key=lambda x: x[1])
        else:  # 按成交量降序
            sorted_data = sorted(data.items(), key=lambda x: x[1], reverse=True)

        prices = [str(p) for p, _ in sorted_data]
        volumes = [v for _, v in sorted_data]

        # 计算总成交量
        total_volume = sum(volumes)

        # 判断图表类型
        if self.bar_chart_radio.isChecked():
            # 准备颜色列表 - 根据符号类型(X/O)设置不同的颜色
            colors = []
            for price, _ in sorted_data:
                symbol = self.symbol_data.get(price, None)
                if symbol == 'X':
                    colors.append('darkred')  # X符号使用红色
                elif symbol == 'O':
                    colors.append('darkblue')  # O符号使用蓝色
                else:
                    colors.append('skyblue')  # 默认颜色

            # 绘制柱状图 - 使用自定义颜色
            bars = ax.bar(prices, volumes, color=colors)

            # 添加标签
            if self.show_percentage_check.isChecked():
                for bar, price, volume in zip(bars, prices, volumes):
                    percentage = volume / total_volume * 100
                    label = f"{percentage:.1f}%"
                    height = bar.get_height()
                    ax.text(
                        bar.get_x() + bar.get_width() / 2,
                        height + 0.1,
                        label,
                        ha='center',
                        va='bottom',
                        rotation=90 if len(bars) > 10 else 0,
                        fontsize=8 if len(bars) > 15 else 10
                    )

            # 设置标题和标签
            ax.set_title("价格区间成交量分布")
            ax.set_xlabel("价格")
            ax.set_ylabel("成交量")

            # 根据数据点数量调整x轴标签
            if len(prices) > 15:
                plt.xticks(rotation=90, fontsize=8)

            # 设置y轴刻度格式为千分位分隔
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, pos: f"{int(x):,}"))

            # 添加图例
            if self.symbol_data:
                x_patch = plt.Rectangle((0, 0), 1, 1, fc="darkred", edgecolor='none')
                o_patch = plt.Rectangle((0, 0), 1, 1, fc="darkblue", edgecolor='none')
                other_patch = plt.Rectangle((0, 0), 1, 1, fc="skyblue", edgecolor='none')
                ax.legend([x_patch, o_patch, other_patch], ['X符号', 'O符号', '其他'], loc='upper right')

        else:
            # 准备颜色列表和标签 - 根据符号类型(X/O)设置不同的颜色
            colors = []
            explode = []  # 设置特定扇形突出显示

            for price, _ in sorted_data:
                symbol = self.symbol_data.get(price, None)
                if symbol == 'X':
                    colors.append('darkred')  # X符号使用红色
                    explode.append(0.05)  # X符号扇形突出一点
                elif symbol == 'O':
                    colors.append('darkblue')  # O符号使用蓝色
                    explode.append(0.05)  # O符号扇形突出一点
                else:
                    colors.append('skyblue')  # 默认颜色
                    explode.append(0)

            # 如果没有符号数据，重置explode
            if not self.symbol_data:
                explode = None

            # 绘制饼图
            wedges, texts, autotexts = ax.pie(
                volumes,
                labels=prices if len(prices) <= 10 else None,
                autopct='%1.1f%%' if self.show_percentage_check.isChecked() else None,
                startangle=90,
                shadow=True,
                colors=colors,
                explode=explode
            )

            # 如果价格太多，使用图例代替直接标签
            if len(prices) > 10:
                ax.legend(
                    wedges,
                    prices,
                    title="价格",
                    loc="center left",
                    bbox_to_anchor=(1, 0, 0.5, 1)
                )

            # 设置标题
            ax.set_title("价格区间成交量分布")

        # 添加说明文本
        total_text = f"总成交量: {total_volume:,}"
        self.figure.text(0.95, 0.01, total_text, ha='right', va='bottom', fontsize=9)

        # 调整布局
        self.figure.tight_layout()

        # 更新图表
        self.canvas.draw()

    def _export_chart(self):
        """导出图表为图片"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出图表", "", "PNG 图片 (*.png);;JPEG 图片 (*.jpg);;PDF 文档 (*.pdf)"
        )
        if file_path:
            self.figure.savefig(file_path, dpi=300, bbox_inches='tight')