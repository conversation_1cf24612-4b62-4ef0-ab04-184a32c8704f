#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图网格渲染器

负责绘制点数图的背景网格线
"""

from typing import Dict, Any, List, Tuple
from PyQt5.QtGui import QPainter, QPen
from PyQt5.QtCore import QRect, Qt

from .base_renderer import BaseRenderer


class GridRenderer(BaseRenderer):
    """
    网格渲染器
    
    负责绘制点数图的背景网格，包括水平价格线和垂直列分隔线
    """
    
    def __init__(self):
        """初始化网格渲染器"""
        super().__init__()
        self._z_index = -10  # 设置为负值确保网格在底层
        self._horizontal_grid_color = "#D0D0D0"  # 水平网格线颜色
        self._vertical_grid_color = "#D0D0D0"    # 垂直网格线颜色
        self._grid_style = Qt.SolidLine            # 网格线样式
        self._grid_width = 1                       # 网格线宽度
        self._show_price_labels = True             # 是否显示价格标签
        self._price_label_font_size = 8            # 价格标签字体大小
        self._show_date_labels = True              # 是否显示日期标签
        self._date_label_font_size = 8             # 日期标签字体大小
        self._price_labels_width = 60              # 价格标签区域宽度
    
    @property
    def horizontal_grid_color(self) -> str:
        """获取水平网格线颜色"""
        return self._horizontal_grid_color
    
    @horizontal_grid_color.setter
    def horizontal_grid_color(self, value: str):
        """设置水平网格线颜色"""
        self._horizontal_grid_color = value
    
    @property
    def vertical_grid_color(self) -> str:
        """获取垂直网格线颜色"""
        return self._vertical_grid_color
    
    @vertical_grid_color.setter
    def vertical_grid_color(self, value: str):
        """设置垂直网格线颜色"""
        self._vertical_grid_color = value
    
    @property
    def grid_style(self) -> int:
        """获取网格线样式"""
        return self._grid_style
    
    @grid_style.setter
    def grid_style(self, value: int):
        """设置网格线样式"""
        self._grid_style = value
    
    @property
    def grid_width(self) -> int:
        """获取网格线宽度"""
        return self._grid_width
    
    @grid_width.setter
    def grid_width(self, value: int):
        """设置网格线宽度"""
        self._grid_width = value
    
    @property
    def show_price_labels(self) -> bool:
        """是否显示价格标签"""
        return self._show_price_labels
    
    @show_price_labels.setter
    def show_price_labels(self, value: bool):
        """设置是否显示价格标签"""
        self._show_price_labels = value
    
    @property
    def price_label_font_size(self) -> int:
        """获取价格标签字体大小"""
        return self._price_label_font_size
    
    @price_label_font_size.setter
    def price_label_font_size(self, value: int):
        """设置价格标签字体大小"""
        self._price_label_font_size = value
    
    def render(self, painter: QPainter, rect: QRect, chart_model: Dict[str, Any],
               cell_width: int, cell_height: int) -> None:
        """
        绘制网格

        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度（像素）
            cell_height: int - 单元格高度（像素）
        """
        if not self.visible:
            return

        # 从图表模型中获取数据
        if not chart_model:
            return

        # 构建视图信息
        view_info = self._build_view_info(chart_model, cell_width, cell_height)

        # 获取必要的视图信息
        price_to_y = view_info.get('price_to_y', {})
        col_to_x = view_info.get('col_to_x', {})
        row_heights = view_info.get('row_heights', [])
        column_widths = view_info.get('column_widths', [])
        prices = view_info.get('prices', [])
        date_labels = view_info.get('date_labels', [])

        # 保存画笔状态
        painter.save()

        # 根据单元格尺寸调整网格线宽度和字体大小
        self._adjust_for_cell_size(cell_width, cell_height)

        # 绘制水平网格线（价格线）
        self._draw_horizontal_grid_lines(painter, rect, price_to_y, prices)

        # 绘制垂直网格线（列分隔线）
        self._draw_vertical_grid_lines(painter, rect, col_to_x)

        # 绘制价格标签
        if self._show_price_labels:
            self._draw_price_labels(painter, rect, price_to_y, prices)

        # 绘制日期标签
        if self._show_date_labels:
            self._draw_date_labels(painter, rect, col_to_x, date_labels)

        # 恢复画笔状态
        painter.restore()
    
    def _draw_horizontal_grid_lines(self, painter: QPainter, rect: QRect, 
                                   price_to_y: Dict[float, int], prices: List[float]) -> None:
        """
        绘制水平网格线
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            price_to_y: Dict[float, int] - 价格到Y坐标的映射
            prices: List[float] - 所有价格点
        """
        # 设置画笔
        pen = QPen(Qt.gray if self._horizontal_grid_color is None else self._horizontal_grid_color)
        pen.setStyle(self._grid_style)
        pen.setWidth(self._grid_width)
        painter.setPen(pen)
        
        # 绘制每条水平线
        for price in prices:
            if price in price_to_y:
                y = price_to_y[price]
                painter.drawLine(self._price_labels_width, y, rect.width(), y)
    
    def _draw_vertical_grid_lines(self, painter: QPainter, rect: QRect, 
                                 col_to_x: Dict[int, int]) -> None:
        """
        绘制垂直网格线
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            col_to_x: Dict[int, int] - 列索引到X坐标的映射
        """
        # 设置画笔
        pen = QPen(Qt.gray if self._vertical_grid_color is None else self._vertical_grid_color)
        pen.setStyle(self._grid_style)
        pen.setWidth(self._grid_width)
        painter.setPen(pen)
        
        # 绘制每条垂直线
        for col_idx, x in col_to_x.items():
            painter.drawLine(x, 0, x, rect.height())
    
    def _draw_price_labels(self, painter: QPainter, rect: QRect, 
                          price_to_y: Dict[float, int], prices: List[float]) -> None:
        """
        绘制价格标签
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            price_to_y: Dict[float, int] - 价格到Y坐标的映射
            prices: List[float] - 所有价格点
        """
        # 设置字体
        font = painter.font()
        font.setPointSize(self._price_label_font_size)
        painter.setFont(font)
        
        # 绘制每个价格标签
        for price in prices:
            if price in price_to_y:
                y = price_to_y[price]
                
                # 绘制背景
                painter.fillRect(0, y - 10, self._price_labels_width, 20, Qt.white)
                
                # 绘制价格文本
                price_text = f"{price:.2f}"
                painter.drawText(5, y + 4, price_text)
    
    def _draw_date_labels(self, painter: QPainter, rect: QRect, 
                         col_to_x: Dict[int, int], date_labels: List[str]) -> None:
        """
        绘制日期标签
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            col_to_x: Dict[int, int] - 列索引到X坐标的映射
            date_labels: List[str] - 日期标签
        """
        # 设置字体
        font = painter.font()
        font.setPointSize(self._date_label_font_size)
        painter.setFont(font)
        
        # 绘制每个日期标签
        for col_idx, x in col_to_x.items():
            if col_idx < len(date_labels):
                date_text = date_labels[col_idx]
                
                # 计算文本宽度
                text_width = painter.fontMetrics().width(date_text)
                
                # 绘制文本（旋转45度）
                painter.save()
                painter.translate(x, rect.height() - 5)
                painter.rotate(-45)
                painter.drawText(-text_width / 2, 0, date_text)
                painter.restore()

    def _build_view_info(self, chart_model: Dict[str, Any], cell_width: int, cell_height: int) -> Dict[str, Any]:
        """
        根据图表模型和单元格尺寸构建视图信息

        参数:
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度

        返回:
            Dict[str, Any] - 视图信息字典
        """
        view_info = {}

        # 获取价格数据和列数据
        price_data = chart_model.get('price_data', [])
        column_data = chart_model.get('column_data', [])

        # 构建价格到Y坐标的映射
        price_to_y = {}
        for i, price in enumerate(price_data):
            price_to_y[price] = i * cell_height + cell_height // 2

        # 构建列索引到X坐标的映射
        col_to_x = {}
        for i in range(len(column_data)):
            col_to_x[i] = i * cell_width + cell_width // 2

        # 构建行高和列宽列表
        row_heights = [cell_height] * len(price_data)
        column_widths = [cell_width] * len(column_data)

        # 构建日期标签（如果有的话）
        date_labels = chart_model.get('date_labels', [])

        view_info.update({
            'price_to_y': price_to_y,
            'col_to_x': col_to_x,
            'row_heights': row_heights,
            'column_widths': column_widths,
            'prices': price_data,
            'date_labels': date_labels
        })

        return view_info

    def _adjust_for_cell_size(self, cell_width: int, cell_height: int) -> None:
        """
        根据单元格尺寸调整网格线宽度和字体大小

        参数:
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度
        """
        # 基础尺寸（20像素）
        base_size = 20

        # 计算缩放比例
        width_scale = cell_width / base_size
        height_scale = cell_height / base_size
        scale_factor = min(width_scale, height_scale)

        # 调整网格线宽度（最小为1像素）
        self._grid_width = max(1, int(1 * scale_factor))

        # 调整字体大小（最小为6像素，最大为16像素）
        base_font_size = 8
        self._price_label_font_size = max(6, min(16, int(base_font_size * scale_factor)))
        self._date_label_font_size = max(6, min(16, int(base_font_size * scale_factor)))

        # 调整价格标签区域宽度
        base_label_width = 60
        self._price_labels_width = max(40, int(base_label_width * scale_factor))