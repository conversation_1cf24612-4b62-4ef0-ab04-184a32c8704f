#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试AkShare外汇数据源修复效果
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

import pandas as pd
from datetime import datetime, timedelta
from services.data_sources.akshare_data_source import AkShareDataSource

def test_forex_historical_data():
    """测试外汇历史数据获取"""
    print("=" * 60)
    print("测试外汇历史数据获取")
    print("=" * 60)
    
    # 创建AkShare数据源实例
    akshare_source = AkShareDataSource()
    
    # 测试参数 - 获取一个月的历史数据
    forex_pairs = [
        "USD/CNY",  # 美元兑人民币
        "EUR/USD",  # 欧元兑美元
        "GBP/USD",  # 英镑兑美元
        "USD/JPY"   # 美元兑日元
    ]
    
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    for forex_code in forex_pairs:
        print(f"\n{'='*40}")
        print(f"测试外汇对: {forex_code}")
        print(f"{'='*40}")
        
        try:
            print(f"正在获取{forex_code}的历史数据...")
            data = akshare_source.get_forex_data(
                code=forex_code,
                start_date=start_date,
                end_date=end_date,
                frequency="daily"
            )
            
            if not data.empty:
                print(f"✅ 成功获取外汇历史数据，共{len(data)}条记录")
                print("数据预览:")
                print(data.head())
                
                # 检查数据完整性
                if 'date' in data.columns:
                    date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
                    expected_count = len(date_range)
                    actual_count = len(data)
                    
                    print(f"预期工作日数量: {expected_count}")
                    print(f"实际数据条数: {actual_count}")
                    
                    if actual_count >= expected_count * 0.8:  # 允许80%的覆盖率
                        print("✅ 数据完整性良好")
                    else:
                        print("⚠️ 数据可能不完整")
                        
                    print(f"日期范围: {data['date'].min()} 到 {data['date'].max()}")
                
                # 检查数据质量
                required_columns = ['open', 'high', 'low', 'close']
                missing_columns = [col for col in required_columns if col not in data.columns]
                if not missing_columns:
                    print("✅ 数据列完整")
                else:
                    print(f"⚠️ 缺少列: {missing_columns}")
                    
            else:
                print("❌ 未获取到外汇历史数据")
                
        except Exception as e:
            print(f"❌ 外汇历史数据获取失败: {e}")

def test_forex_realtime_data():
    """测试外汇实时数据获取"""
    print("\n" + "=" * 60)
    print("测试外汇实时数据获取")
    print("=" * 60)
    
    # 创建AkShare数据源实例
    akshare_source = AkShareDataSource()
    
    # 测试参数 - 获取当日数据
    forex_pairs = [
        "USD/CNY",  # 美元兑人民币
        "EUR/CNY",  # 欧元兑人民币
        "GBP/CNY",  # 英镑兑人民币
    ]
    
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    for forex_code in forex_pairs:
        print(f"\n{'='*40}")
        print(f"测试外汇对: {forex_code}")
        print(f"{'='*40}")
        
        try:
            print(f"正在获取{forex_code}的实时数据...")
            data = akshare_source.get_forex_data(
                code=forex_code,
                start_date=current_date,
                end_date=current_date,
                frequency="daily"
            )
            
            if not data.empty:
                print(f"✅ 成功获取外汇实时数据，共{len(data)}条记录")
                print("数据预览:")
                print(data.head())
                
                # 检查汇率值是否合理
                if 'close' in data.columns:
                    close_rate = data['close'].iloc[0]
                    if close_rate > 0:
                        print(f"✅ 汇率值合理: {close_rate}")
                    else:
                        print(f"⚠️ 汇率值异常: {close_rate}")
                        
            else:
                print("❌ 未获取到外汇实时数据")
                
        except Exception as e:
            print(f"❌ 外汇实时数据获取失败: {e}")

def test_enhanced_forex():
    """测试增强版外汇数据获取"""
    print("\n" + "=" * 60)
    print("测试增强版外汇数据获取")
    print("=" * 60)
    
    try:
        from services.data_sources.enhanced_akshare_data_source import EnhancedAkShareDataSource
        
        # 创建增强版AkShare数据源实例
        enhanced_source = EnhancedAkShareDataSource()
        
        # 测试外汇数据
        print("测试增强版外汇数据获取...")
        data = enhanced_source.get_forex_data(
            code="USD/CNY",
            start_date="2024-01-01",
            end_date="2024-01-15",
            frequency="daily"
        )
        
        if not data.empty:
            print(f"✅ 增强版外汇数据获取成功，共{len(data)}条记录")
            print("数据预览:")
            print(data.head())
            
            # 检查数据质量
            if 'date' in data.columns:
                print(f"日期范围: {data['date'].min()} 到 {data['date'].max()}")
            
        else:
            print("❌ 增强版外汇数据获取失败")
            
    except Exception as e:
        print(f"❌ 增强版外汇测试失败: {e}")

def main():
    """主函数"""
    print("AkShare外汇数据源修复测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试外汇历史数据
    test_forex_historical_data()
    
    # 测试外汇实时数据
    test_forex_realtime_data()
    
    # 测试增强版外汇数据
    test_enhanced_forex()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
