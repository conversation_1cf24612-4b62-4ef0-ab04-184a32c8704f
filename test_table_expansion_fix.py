#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试表格预扩展修复功能
"""

import sys
import os

# 添加src目录到路径
src_dir = os.path.join(os.path.dirname(__file__), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from utils.settings_manager import SettingsManager


def test_table_expansion_fix():
    """测试表格预扩展修复功能"""
    print("=== 测试表格预扩展修复功能 ===")
    
    # 创建设置管理器
    settings = SettingsManager()
    
    print("\n1. 测试设置项读取:")
    
    # 测试旧的extra_columns设置
    old_extra_columns = settings.get_setting("chart", "extra_columns", 3)
    print(f"   旧的extra_columns设置: {old_extra_columns}")
    
    # 测试新的expand_right_columns设置
    new_expand_right = settings.get_setting("table_expansion", "expand_right_columns", 10)
    print(f"   新的expand_right_columns设置: {new_expand_right}")
    
    print("\n2. 测试设置修改:")
    
    # 修改新的设置值
    settings.set_setting("table_expansion", "expand_right_columns", 15)
    settings.save_settings()
    
    # 重新读取验证
    new_value = settings.get_setting("table_expansion", "expand_right_columns", 10)
    print(f"   修改后的expand_right_columns: {new_value}")
    
    if new_value == 15:
        print("   ✅ 设置修改成功")
    else:
        print("   ❌ 设置修改失败")
    
    print("\n3. 测试点数图模型:")
    
    try:
        from models.point_figure_model import PointFigureModel
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        test_data = pd.DataFrame({
            'date': dates,
            'open': np.random.uniform(10, 12, 10),
            'high': np.random.uniform(12, 14, 10),
            'low': np.random.uniform(8, 10, 10),
            'close': np.random.uniform(10, 12, 10),
            'volume': np.random.randint(1000, 5000, 10)
        })
        
        # 创建点数图模型
        model = PointFigureModel()
        
        # 生成点数图
        chart_data = model.generate_point_figure_chart(
            data=test_data,
            box_size=0.5,
            reversal_amount=3,
            method="high_low"
        )
        
        print(f"   生成的点数图列数: {len(chart_data.columns)}")
        print(f"   预期列数应该包含设置的扩展列数: {new_value}")
        
        # 检查是否使用了新的设置值
        if len(chart_data.columns) > 10:  # 假设原始数据会生成一些列
            print("   ✅ 点数图生成成功，包含扩展列")
        else:
            print("   ⚠️ 点数图生成成功，但扩展列数可能不正确")
            
    except Exception as e:
        print(f"   ❌ 点数图模型测试失败: {str(e)}")
    
    print("\n4. 恢复默认设置:")
    
    # 恢复默认设置
    settings.set_setting("table_expansion", "expand_right_columns", 10)
    settings.save_settings()
    print("   已恢复默认设置")
    
    print("\n=== 测试完成 ===")


def test_settings_integration():
    """测试设置集成"""
    print("\n=== 测试设置集成 ===")
    
    settings = SettingsManager()
    
    # 测试所有表格预扩展设置
    expand_up = settings.get_setting("table_expansion", "expand_up_rows", 10)
    expand_down = settings.get_setting("table_expansion", "expand_down_rows", 10)
    expand_right = settings.get_setting("table_expansion", "expand_right_columns", 10)
    
    print(f"向上扩展行数: {expand_up}")
    print(f"向下扩展行数: {expand_down}")
    print(f"向右扩展列数: {expand_right}")
    
    # 验证默认值
    if expand_up == 10 and expand_down == 10 and expand_right == 10:
        print("✅ 所有设置项默认值正确")
    else:
        print("❌ 设置项默认值不正确")
    
    # 测试设置修改
    settings.set_setting("table_expansion", "expand_up_rows", 15)
    settings.set_setting("table_expansion", "expand_down_rows", 20)
    settings.set_setting("table_expansion", "expand_right_columns", 12)
    
    if settings.save_settings():
        print("✅ 设置保存成功")
    else:
        print("❌ 设置保存失败")
    
    # 验证修改
    new_up = settings.get_setting("table_expansion", "expand_up_rows", 10)
    new_down = settings.get_setting("table_expansion", "expand_down_rows", 10)
    new_right = settings.get_setting("table_expansion", "expand_right_columns", 10)
    
    if new_up == 15 and new_down == 20 and new_right == 12:
        print("✅ 设置修改验证成功")
    else:
        print("❌ 设置修改验证失败")
    
    # 恢复默认值
    settings.set_setting("table_expansion", "expand_up_rows", 10)
    settings.set_setting("table_expansion", "expand_down_rows", 10)
    settings.set_setting("table_expansion", "expand_right_columns", 10)
    settings.save_settings()


if __name__ == "__main__":
    test_table_expansion_fix()
    test_settings_integration()
