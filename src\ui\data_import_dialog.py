#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据导入对话框
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFileDialog, QComboBox, QFormLayout,
                            QGroupBox, QRadioButton, QButtonGroup, QMessageBox,
                            QLineEdit, QCheckBox, QDialogButtonBox, QTabWidget,
                            QWidget, QGridLayout, QSpinBox, QProgressDialog,
                            QDateEdit)
from PyQt5.QtCore import Qt, QTimer, QDate

# 添加对设置管理器的引用
from utils.settings_manager import SettingsManager
from ui.data_source_selection_dialog import DataSourceSelectionDialog


class DataImportDialog(QDialog):
    """数据导入对话框类"""
    
    def __init__(self, data_controller, parent=None):
        """初始化数据导入对话框"""
        super().__init__(parent)
        
        # 保存数据控制器引用
        self.data_controller = data_controller
        
        # 获取设置管理器
        self.settings = SettingsManager()
        
        # 初始化界面
        self._init_ui()
        
        # 尝试加载上次打开的目录
        self._load_last_directory()
        
        # 连接文件类型变更信号
        self.file_type_combo.currentIndexChanged.connect(self._on_file_type_changed)
        
        # 更新股票代码提示
        self._update_stock_code_hint()
    
    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("导入数据")
        self.setMinimumWidth(500)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 创建文件导入标签页
        file_tab = QWidget()
        file_layout = QVBoxLayout(file_tab)
        
        # 创建文件选择组
        file_group = QGroupBox("选择数据文件")
        file_form_layout = QFormLayout(file_group)
        
        # 文件路径输入框
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        
        # 浏览按钮
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self._browse_file)
        
        # 创建文件路径布局
        file_path_layout = QHBoxLayout()
        file_path_layout.addWidget(self.file_path_edit)
        file_path_layout.addWidget(browse_button)
        
        file_form_layout.addRow("文件路径:", file_path_layout)
        
        # 文件类型选择
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(["自动检测", "CSV文件", "Excel文件", "通达信数据"])
        file_form_layout.addRow("文件类型:", self.file_type_combo)
        
        # 添加文件选择组到布局
        file_layout.addWidget(file_group)
        
        # 创建数据格式组
        self.format_group = QGroupBox("数据格式设置")
        format_form_layout = QFormLayout(self.format_group)
        
        # 日期列选择
        self.date_column_combo = QComboBox()
        self.date_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列"])
        format_form_layout.addRow("日期列:", self.date_column_combo)
        
        # 开盘价列选择
        self.open_column_combo = QComboBox()
        self.open_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列"])
        self.open_column_combo.setCurrentIndex(1)  # 默认选择第2列
        format_form_layout.addRow("开盘价列:", self.open_column_combo)
        
        # 最高价列选择
        self.high_column_combo = QComboBox()
        self.high_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列"])
        self.high_column_combo.setCurrentIndex(2)  # 默认选择第3列
        format_form_layout.addRow("最高价列:", self.high_column_combo)
        
        # 最低价列选择
        self.low_column_combo = QComboBox()
        self.low_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列"])
        self.low_column_combo.setCurrentIndex(3)  # 默认选择第4列
        format_form_layout.addRow("最低价列:", self.low_column_combo)
        
        # 收盘价列选择
        self.close_column_combo = QComboBox()
        self.close_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列"])
        self.close_column_combo.setCurrentIndex(4)  # 默认选择第5列
        format_form_layout.addRow("收盘价列:", self.close_column_combo)
        
        # 成交量列选择
        self.volume_column_combo = QComboBox()
        self.volume_column_combo.addItems(["第1列", "第2列", "第3列", "第4列", "第5列", "第6列"])
        self.volume_column_combo.setCurrentIndex(5)  # 默认选择第6列
        format_form_layout.addRow("成交量列:", self.volume_column_combo)
        
        # 添加数据格式组到布局
        file_layout.addWidget(self.format_group)
        
        # 创建通达信数据信息组（默认隐藏）
        self.tdx_info_group = QGroupBox("通达信数据信息")
        self.tdx_info_group.setVisible(False)
        tdx_info_layout = QVBoxLayout(self.tdx_info_group)
        
        # 添加通达信数据格式说明
        tdx_info_label = QLabel(
            "选择通达信数据将自动进行智能处理:\n"
            "- 自动识别TXT和Excel格式文件\n"
            "- 自动提取股票代码和名称\n"
            "- 自动识别日期、价格和成交量数据\n"
            "- 支持日K线、周K线、月K线和分钟K线数据\n\n"
            "您无需手动设置数据列映射，系统将自动处理"
        )
        tdx_info_label.setWordWrap(True)
        tdx_info_layout.addWidget(tdx_info_label)
        
        # 添加通达信数据信息组到布局
        file_layout.addWidget(self.tdx_info_group)
        
        # 添加文件导入标签页
        tab_widget.addTab(file_tab, "文件导入")
        
        # 创建在线数据标签页
        online_tab = QWidget()
        online_layout = QVBoxLayout(online_tab)
        
        # 创建数据源组
        source_group = QGroupBox("数据源设置")
        source_form_layout = QFormLayout(source_group)
        
        # 当前数据源显示
        self.current_source_label = QLabel("未选择")
        
        # 选择数据源按钮
        select_source_button = QPushButton("选择数据源...")
        select_source_button.clicked.connect(self._select_data_source)
        
        # 数据源布局
        source_layout = QHBoxLayout()
        source_layout.addWidget(self.current_source_label, 1)  # 1是拉伸因子
        source_layout.addWidget(select_source_button)
        
        source_form_layout.addRow("当前数据源:", source_layout)
        
        # 添加配置API密钥按钮
        config_api_button = QPushButton("配置API密钥...")
        config_api_button.clicked.connect(self._configure_api_key)
        source_form_layout.addRow("API密钥:", config_api_button)
        
        # 添加数据源组到布局
        online_layout.addWidget(source_group)
        
        # 创建股票信息组
        stock_group = QGroupBox("股票信息")
        stock_form_layout = QFormLayout(stock_group)
        
        # 股票代码输入框
        self.stock_code_edit = QLineEdit()
        stock_form_layout.addRow("股票代码:", self.stock_code_edit)
        
        # 添加提示标签
        self.code_tip_label = QLabel("选择数据源后，这里将显示对应的代码格式说明")
        self.code_tip_label.setWordWrap(True)
        self.code_tip_label.setStyleSheet("color: #FF0000;")  # 设置红色
        self.code_tip_label.setMinimumHeight(80)  # 设置最小高度以容纳多行文本
        stock_form_layout.addRow("", self.code_tip_label)
        
        # 添加复权类型选择
        self.adjust_combo = QComboBox()
        self.adjust_combo.addItems(["前复权", "后复权", "不复权"])
        self.adjust_combo.setCurrentIndex(0)  # 默认选中前复权
        stock_form_layout.addRow("复权方式:", self.adjust_combo)
        
        # 添加周期选择
        self.frequency_combo = QComboBox()
        # 设置周期项目（文本和关联数据值）
        self.frequency_combo.addItem("日线", "daily")
        self.frequency_combo.addItem("周线", "weekly")
        self.frequency_combo.addItem("月线", "monthly")
        self.frequency_combo.addItem("5分钟线", "5min")
        self.frequency_combo.addItem("15分钟线", "15min")
        self.frequency_combo.addItem("30分钟线", "30min")
        self.frequency_combo.addItem("60分钟线", "60min")
        self.frequency_combo.setCurrentIndex(0)  # 默认选中日线
        stock_form_layout.addRow("数据周期:", self.frequency_combo)
        
        # 添加股票信息组到布局
        online_layout.addWidget(stock_group)
        
        # 创建时间范围组
        time_group = QGroupBox("时间范围")
        time_layout = QVBoxLayout(time_group)
        
        # 日期选择表单布局
        time_form_layout = QFormLayout()
        time_layout.addLayout(time_form_layout)
        
        # 开始日期选择器
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDisplayFormat("yyyy-MM-dd")
        # 设置默认日期为前一年的今天
        default_start_date = QDate.currentDate().addYears(-1)
        self.start_date_edit.setDate(default_start_date)
        self.start_date_edit.setMinimumDate(QDate(2000, 1, 1))
        self.start_date_edit.setMaximumDate(QDate.currentDate())
        time_form_layout.addRow("开始日期:", self.start_date_edit)
        
        # 结束日期选择器
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDisplayFormat("yyyy-MM-dd")
        # 设置默认日期为今天
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setMinimumDate(QDate(2000, 1, 1))
        self.end_date_edit.setMaximumDate(QDate.currentDate())
        time_form_layout.addRow("结束日期:", self.end_date_edit)
        
        # 添加日期快捷按钮
        date_buttons_layout = QHBoxLayout()
        time_layout.addLayout(date_buttons_layout)
        
        # 快捷按钮：最近一个月、三个月、六个月、一年、所有数据
        btn_1m = QPushButton("最近1月")
        btn_3m = QPushButton("最近3月")
        btn_6m = QPushButton("最近6月")
        btn_1y = QPushButton("最近1年")
        btn_all = QPushButton("全部数据")
        
        # 连接按钮事件
        btn_1m.clicked.connect(lambda: self._set_quick_date_range(months=1))
        btn_3m.clicked.connect(lambda: self._set_quick_date_range(months=3))
        btn_6m.clicked.connect(lambda: self._set_quick_date_range(months=6))
        btn_1y.clicked.connect(lambda: self._set_quick_date_range(years=1))
        btn_all.clicked.connect(lambda: self._set_quick_date_range(all_data=True))
        
        # 添加按钮到布局
        date_buttons_layout.addWidget(btn_1m)
        date_buttons_layout.addWidget(btn_3m)
        date_buttons_layout.addWidget(btn_6m)
        date_buttons_layout.addWidget(btn_1y)
        date_buttons_layout.addWidget(btn_all)
        
        # 添加时间范围组到布局
        online_layout.addWidget(time_group)
        
        # 添加日期范围提示
        date_tip_label = QLabel("支持任意日期范围的数据下载，从2006年至今")
        date_tip_label.setWordWrap(True)
        date_tip_label.setStyleSheet("color: gray;")
        online_layout.addWidget(date_tip_label)
        
        # 添加在线数据标签页
        tab_widget.addTab(online_tab, "在线数据")
        
        # 添加标签页到主布局
        main_layout.addWidget(tab_widget)
        
        # 创建按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self._import_data)
        button_box.rejected.connect(self.reject)
        
        # 添加按钮到主布局
        main_layout.addWidget(button_box)
        
        # 保存标签页引用
        self.tab_widget = tab_widget
        
        # 在初始化对话框后更新当前数据源显示
        self._update_current_source_display()
    
    def _on_file_type_changed(self, index):
        """文件类型变更处理"""
        # 当选择通达信数据时，隐藏列映射设置，显示通达信数据信息
        if index == 3:  # 通达信数据
            self.format_group.setVisible(False)
            self.tdx_info_group.setVisible(True)
        else:
            self.format_group.setVisible(True)
            self.tdx_info_group.setVisible(False)
    
    def _load_last_directory(self):
        """加载上次打开的目录"""
        # 优先使用上次打开的目录
        last_dir = self.settings.get_setting("data", "last_directory", "")
        
        # 检查上次使用的目录是否仍然存在
        if last_dir and os.path.exists(last_dir):
            self.last_directory = last_dir
            print(f"[DataImportDialog] 使用上次打开的目录: {last_dir}")
            return
        
        # 如果上次目录不存在，尝试使用智能默认路径
        smart_paths = [
            # 常见的点数图数据目录
            os.path.expanduser("~/Documents/点数图数据"),
            os.path.expanduser("~/Documents/股票数据"),
            os.path.expanduser("~/Documents/数据文件"),
            os.path.expanduser("~/Desktop/数据"),
            # 项目目录下的数据文件夹
            os.path.join(os.getcwd(), "data"),
            os.path.join(os.getcwd(), "examples"),
            # 用户文档目录（最后的备选）
            os.path.expanduser("~/Documents")
        ]
        
        # 选择第一个存在的目录
        for path in smart_paths:
            if os.path.exists(path):
                self.last_directory = path
                print(f"[DataImportDialog] 使用智能默认目录: {path}")
                return
        
        # 如果所有路径都不存在，使用用户文档目录
        self.last_directory = os.path.expanduser("~/Documents")
        print(f"[DataImportDialog] 使用默认文档目录: {self.last_directory}")
    
    def _browse_file(self):
        """浏览文件"""
        # 确保起始目录存在，如果不存在则使用父目录或默认目录
        start_directory = self.last_directory
        if not os.path.exists(start_directory):
            # 尝试使用父目录
            parent_dir = os.path.dirname(start_directory)
            if os.path.exists(parent_dir):
                start_directory = parent_dir
            else:
                start_directory = os.path.expanduser("~/Documents")
            print(f"[DataImportDialog] 起始目录不存在，使用: {start_directory}")
        
        # 从确认存在的目录开始浏览
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据文件", start_directory, 
            "所有支持的文件 (*.csv *.xlsx *.xls *.txt);;CSV文件 (*.csv);;Excel文件 (*.xlsx *.xls);;文本文件 (*.txt)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            
            # 保存当前目录作为下次打开的目录
            current_dir = os.path.dirname(file_path)
            
            # 验证目录路径的有效性
            if os.path.exists(current_dir) and os.path.isdir(current_dir):
                self.last_directory = current_dir
                
                # 保存到设置中，增加错误处理
                try:
                    self.settings.set_setting("data", "last_directory", current_dir)
                    self.settings.save_settings()
                    print(f"[DataImportDialog] 路径已保存: {current_dir}")
                except Exception as e:
                    print(f"[DataImportDialog] 保存路径失败: {e}")
            else:
                print(f"[DataImportDialog] 无效的目录路径: {current_dir}")
            
            # 根据文件扩展名自动选择文件类型
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            
            if ext == '.csv':
                self.file_type_combo.setCurrentIndex(1)
            elif ext in ['.xlsx', '.xls']:
                self.file_type_combo.setCurrentIndex(2)
            elif ext == '.txt':
                # 尝试检测是否为通达信文件（通常文件名中包含K线或者文件内容中有特定格式）
                try:
                    with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                        first_few_lines = ''.join(f.readline() for _ in range(10))
                    
                    # 检查是否含有通达信特征
                    tdx_features = ['日期', '开盘', '收盘', '最高', '最低', 'K线', '日线', '周线', '月线']
                    if any(feature in first_few_lines for feature in tdx_features) or 'K线' in file_path:
                        self.file_type_combo.setCurrentIndex(3)  # 通达信数据
                    else:
                        self.file_type_combo.setCurrentIndex(0)  # 自动检测
                except Exception:
                    self.file_type_combo.setCurrentIndex(0)  # 自动检测
    
    def _import_data(self):
        """导入数据"""
        # 获取当前标签页索引
        current_tab = self.tab_widget.currentIndex()
        
        try:
            if current_tab == 0:  # 文件导入
                # 获取文件路径
                file_path = self.file_path_edit.text()
                
                if not file_path:
                    QMessageBox.warning(self, "警告", "请选择数据文件")
                    return
                
                # 获取文件类型
                file_type_index = self.file_type_combo.currentIndex()
                file_type = None
                if file_type_index == 1:
                    file_type = "csv"
                elif file_type_index == 2:
                    file_type = "excel"
                elif file_type_index == 3:
                    file_type = "tdx"
                
                # 获取列映射（如果不是通达信数据）
                column_mapping = None
                if file_type_index != 3:  # 不是通达信数据
                    column_mapping = {
                        'date': self.date_column_combo.currentIndex(),
                        'open': self.open_column_combo.currentIndex(),
                        'high': self.high_column_combo.currentIndex(),
                        'low': self.low_column_combo.currentIndex(),
                        'close': self.close_column_combo.currentIndex(),
                        'volume': self.volume_column_combo.currentIndex()
                    }
                
                # 导入数据
                result = self.data_controller.load_data(file_path, file_type, column_mapping)
                
                if not result:
                    QMessageBox.warning(self, "导入失败", "没有可用数据，请检查文件格式或选择正确的文件类型")
                    return
                
            else:  # 在线数据
                # 获取股票代码
                stock_code = self.stock_code_edit.text()
                
                if not stock_code:
                    QMessageBox.warning(self, "警告", "请输入股票代码")
                    return
                
                # 获取日期范围 - 使用QDateEdit获取格式化的日期字符串
                start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
                end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
                
                # 创建进度对话框
                progress = QProgressDialog("正在下载股票数据...", "取消", 0, 100, self)
                progress.setWindowTitle("数据下载中")
                progress.setWindowModality(Qt.WindowModal)
                progress.setMinimumDuration(0)  # 立即显示
                progress.setAutoClose(True)  # 完成时自动关闭
                progress.setValue(0)
                
                # 连接进度信号
                self.data_controller.download_progress.connect(progress.setValue)
                
                # 连接取消信号
                canceled = [False]  # 使用列表作为可变引用
                def on_canceled():
                    canceled[0] = True
                    progress.setLabelText("正在取消操作...")
                
                progress.canceled.connect(on_canceled)
                
                # 显示进度对话框
                progress.show()
                
                # 确保UI更新
                QTimer.singleShot(100, lambda: self._do_import_online_data(stock_code, None, start_date, end_date, progress, canceled))
                
                # 这里不立即返回，让进度对话框显示出来
                return
            
            # 接受对话框
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "导入错误", str(e))
    
    def _set_quick_date_range(self, months=0, years=0, all_data=False):
        """设置快速日期范围"""
        # 设置结束日期为今天
        today = QDate.currentDate()
        self.end_date_edit.setDate(today)
        
        # 根据选择设置开始日期
        if all_data:
            # 设置为BaoStock支持的最早日期
            self.start_date_edit.setDate(QDate(2006, 1, 1))
        else:
            # 计算开始日期
            start_date = today.addMonths(-months).addYears(-years)
            self.start_date_edit.setDate(start_date)
    
    def _update_current_source_display(self):
        """更新当前数据源显示"""
        try:
            # 获取当前数据源名称
            current_source = self.data_controller.stock_data_service.get_current_source()
            if current_source:
                # 设置显示数据源名称和支持的市场
                source_name = current_source.get_name()
                self.current_source_label.setText(source_name)
                
                # 更新复权和周期选项
                self._update_adjust_options()
                self._update_frequency_options()
            else:
                self.current_source_label.setText("未选择数据源")
        except Exception as e:
            self.current_source_label.setText("获取数据源信息失败")
            self.logger.error(f"更新数据源显示时出错: {e}")
    
    def _update_adjust_options(self):
        """更新复权选项"""
        # 保存当前选择的索引
        current_index = self.adjust_combo.currentIndex()
        
        # 清空下拉框
        self.adjust_combo.clear()
        
        # 添加固定的选项（所有数据源都支持这些标准选项）
        self.adjust_combo.addItems(["前复权", "后复权", "不复权"])
        
        # 尝试还原之前选择的索引
        if current_index >= 0 and current_index < self.adjust_combo.count():
            self.adjust_combo.setCurrentIndex(current_index)
        else:
            self.adjust_combo.setCurrentIndex(0)  # 默认选中前复权
    
    def _update_frequency_options(self):
        """更新周期选项"""
        # 保存当前选择的文本
        current_text = self.frequency_combo.currentText()
        
        # 清空下拉框
        self.frequency_combo.clear()
        
        try:
            # 获取当前数据源支持的周期
            current_source = self.data_controller.stock_data_service.get_current_source()
            if current_source:
                frequencies = current_source.get_supported_frequencies()
                
                # 添加支持的周期
                for freq_code, freq_name in frequencies.items():
                    # 保存周期码作为数据
                    self.frequency_combo.addItem(freq_name, freq_code)
            else:
                # 添加默认周期
                default_frequencies = {
                    "d": "日线", 
                    "w": "周线", 
                    "m": "月线",
                    "5": "5分钟线",
                    "15": "15分钟线",
                    "30": "30分钟线",
                    "60": "60分钟线"
                }
                for freq_code, freq_name in default_frequencies.items():
                    self.frequency_combo.addItem(freq_name, freq_code)
        except Exception as e:
            # 添加默认周期
            self.frequency_combo.addItems(["日线", "周线", "月线", "5分钟线", "15分钟线", "30分钟线", "60分钟线"])
            self.logger.error(f"更新周期选项时出错: {e}")
        
        # 尝试还原之前选择的选项
        index = self.frequency_combo.findText(current_text)
        if index >= 0:
            self.frequency_combo.setCurrentIndex(index)
        else:
            # 默认选中日线
            index = self.frequency_combo.findText("日线")
            if index >= 0:
                self.frequency_combo.setCurrentIndex(index)
    
    def _select_data_source(self):
        """选择数据源"""
        # 创建数据源选择对话框
        dialog = DataSourceSelectionDialog(self.data_controller.stock_data_service, self)
        
        # 如果用户接受对话框（点击确定）
        if dialog.exec_() == QDialog.Accepted:
            # 获取选择的数据源
            selected_source_id = dialog.get_selected_source_id()
            
            # 设置当前数据源
            if self.data_controller.stock_data_service.set_current_source(selected_source_id):
                # 更新数据源显示
                self._update_current_source_display()
                # 更新股票代码提示文本
                self._update_stock_code_hint()
            else:
                QMessageBox.warning(self, "设置失败", "无法设置选择的数据源")
    
    def _update_stock_code_hint(self):
        """更新股票代码输入框的提示文本，根据所选数据源自定义提示"""
        try:
            # 获取当前数据源
            current_source = self.data_controller.stock_data_service.get_current_source()
            if not current_source:
                return
            
            source_name = current_source.get_name()
            
            # 根据数据源类型设置不同的提示文本
            if "BaoStock" in source_name:
                hint_text = (
                    "A股代码格式示例：\n"
                    "- 上交所股票：600000（不需要市场前缀）\n"
                    "- 深交所股票：000001（不需要市场前缀）\n"
                    "- 创业板股票：300001（不需要市场前缀）\n"
                    "- 科创板股票：688001（不需要市场前缀）\n"
                    "- 指数代码：sh.000001（上证指数）, sz.399001（深证成指）"
                )
            elif "Yahoo" in source_name:
                hint_text = (
                    "股票代码格式示例：\n"
                    "- A股代码：600000.SS（上交所）, 000001.SZ（深交所）\n"
                    "- 港股代码：00700.HK（腾讯控股）\n"
                    "- 美股代码：AAPL（苹果）, MSFT（微软）\n"
                    "- 美股指数：^GSPC（标普500）, ^DJI（道琼斯）"
                )
            elif "AkShare" in source_name:
                hint_text = (
                    "多市场代码格式示例：\n"
                    "- A股代码：600000（上交所）, 000001（深交所）\n"
                    "- 港股代码：00700（腾讯控股，不需要.HK后缀）\n"
                    "- 美股代码：AAPL（苹果）, MSFT（微软）\n"
                    "- 期货代码：rb2405（螺纹钢）, IF2406（股指期货）\n"
                    "- 外汇代码：USD/CNY（美元/人民币）, EUR/USD（欧元/美元）"
                )
            else:
                # 默认提示
                hint_text = "输入证券代码，系统将尝试自动识别市场类型"
                
            # 更新提示标签文本
            self.code_tip_label.setText(hint_text)
            
        except Exception as e:
            self.logger.error(f"更新股票代码提示时出错: {e}")
    
    def _configure_api_key(self):
        """配置API密钥"""
        # 创建API密钥对话框
        from ui.api_key_dialog import ApiKeyDialog
        api_key_dialog = ApiKeyDialog(
            self.data_controller.stock_data_service.source_factory,
            self.data_controller.stock_data_service.api_key_manager,
            self
        )
        
        # 显示对话框
        api_key_dialog.exec_()
    
    def _do_import_online_data(self, stock_code, market, start_date, end_date, progress, canceled):
        """执行在线数据导入操作"""
        try:
            # 获取复权参数
            adjust_index = self.adjust_combo.currentIndex()
            adjust_flag = "2"  # 默认前复权
            if adjust_index == 1:
                adjust_flag = "1"  # 后复权
            elif adjust_index == 2:
                adjust_flag = "3"  # 不复权
            
            # 获取周期参数
            frequency_index = self.frequency_combo.currentIndex()
            # 获取当前选项的数据值（周期码）
            frequency = self.frequency_combo.itemData(frequency_index)
            if not frequency:
                # 如果没有数据值，使用默认值
                frequency = "daily"
            
            # 导入在线数据（传入复权参数但不传入市场参数，让服务自动判断）
            if not canceled[0]:
                result = self.data_controller.load_online_data(
                    stock_code=stock_code, 
                    market=None,  # 设为None，让服务自动判断市场
                    start_date=start_date, 
                    end_date=end_date,
                    adjust_flag=adjust_flag,
                    frequency=frequency
                )
                
                if not result:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "导入失败", "获取在线数据失败")
                    progress.close()
                    return
                
                # 显示成功消息以及查看数据选项
                from PyQt5.QtWidgets import QMessageBox
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("导入成功")
                
                # 添加关于实际日期范围的信息
                actual_date_info = ""
                if hasattr(self.data_controller, 'actual_start_date') and hasattr(self.data_controller, 'actual_end_date'):
                    requested_range = f"{start_date} 至 {end_date}"
                    actual_range = f"{self.data_controller.actual_start_date} 至 {self.data_controller.actual_end_date}"
                    
                    actual_date_info = f"\n\n请求日期范围: {requested_range}\n实际数据范围: {actual_range}"
                    
                    # 如果实际范围与请求范围不同，添加提示
                    if actual_range != requested_range:
                        actual_date_info += "\n\n注意：实际数据范围可能小于请求范围，这通常是因为数据源仅提供有实际交易数据的日期。"
                
                msg_box.setText(f"在线数据导入成功。{actual_date_info}")
                msg_box.setInformativeText("是否立即查看数据内容？")
                msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                msg_box.setDefaultButton(QMessageBox.Yes)
                
                if msg_box.exec_() == QMessageBox.Yes:
                    # 导入数据查看器对话框
                    from ui.data_viewer_dialog import DataViewerDialog
                    DataViewerDialog.show_data(self.data_controller, self)
                
                # 接受对话框
                self.accept()
        except Exception as e:
            progress.close()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "导入错误", str(e)) 