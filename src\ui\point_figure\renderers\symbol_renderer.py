#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图符号渲染器

负责绘制点数图的X和O符号
"""

from typing import Dict, Any, List, Tuple
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import QRect, Qt, QPointF, QRectF

from .base_renderer import BaseRenderer


class SymbolRenderer(BaseRenderer):
    """
    符号渲染器
    
    负责绘制点数图的X和O符号
    """
    
    def __init__(self):
        """初始化符号渲染器"""
        super().__init__()
        self._z_index = 0  # 在网格之上绘制符号
        self._x_color = "#0000FF"  # X符号颜色（上涨）
        self._o_color = "#FF0000"  # O符号颜色（下跌）
        self._symbol_size_percent = 0.85  # 符号大小占格子的百分比
        self._x_width = 1.5  # X符号线宽
        self._o_width = 1.5  # O符号线宽
        self._filled_symbols = False  # 是否填充符号
        self._highlight_last_column = True  # 是否高亮最后一列
        self._last_column_highlight_color = "#FFFF00"  # 最后一列高亮颜色
    
    @property
    def x_color(self) -> str:
        """获取X符号颜色"""
        return self._x_color
    
    @x_color.setter
    def x_color(self, value: str):
        """设置X符号颜色"""
        self._x_color = value
    
    @property
    def o_color(self) -> str:
        """获取O符号颜色"""
        return self._o_color
    
    @o_color.setter
    def o_color(self, value: str):
        """设置O符号颜色"""
        self._o_color = value
    
    @property
    def symbol_size_percent(self) -> float:
        """获取符号大小百分比"""
        return self._symbol_size_percent
    
    @symbol_size_percent.setter
    def symbol_size_percent(self, value: float):
        """设置符号大小百分比"""
        if 0.1 <= value <= 1.0:
            self._symbol_size_percent = value
    
    @property
    def filled_symbols(self) -> bool:
        """是否填充符号"""
        return self._filled_symbols
    
    @filled_symbols.setter
    def filled_symbols(self, value: bool):
        """设置是否填充符号"""
        self._filled_symbols = value
    
    def render(self, painter: QPainter, rect: QRect, chart_model: Dict[str, Any],
               cell_width: int, cell_height: int) -> None:
        """
        绘制符号

        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度（像素）
            cell_height: int - 单元格高度（像素）
        """
        if not self.visible:
            return

        # 从图表模型中获取数据
        if not chart_model:
            return

        # 构建视图信息
        view_info = self._build_view_info(chart_model, cell_width, cell_height)

        # 获取必要的视图信息
        chart_data = view_info.get('chart_data')
        price_to_y = view_info.get('price_to_y', {})
        col_to_x = view_info.get('col_to_x', {})
        
        if chart_data is None or chart_data.empty:
            return
        
        # 保存画笔状态
        painter.save()

        # 根据单元格尺寸调整符号线宽
        self._adjust_for_cell_size(cell_width, cell_height)

        # 获取列数
        num_cols = len(chart_data.columns)
        
        # 遍历每一列
        for col_idx in range(num_cols):
            col_name = chart_data.columns[col_idx]
            col = chart_data[col_name]
            
            # 获取列中心X坐标
            if col_idx not in col_to_x:
                continue
            
            center_x = col_to_x[col_idx]
            
            # 检查是否是最后一列（用于高亮）
            is_last_column = (col_idx == num_cols - 1)
            
            # 遍历该列的每个非空单元格
            for price, value in col.items():
                if pd.notna(value):  # 只处理非空值
                    # 获取价格对应的Y坐标
                    if price not in price_to_y:
                        continue
                    
                    center_y = price_to_y[price]
                    
                    # 计算符号尺寸
                    symbol_width = cell_width * self._symbol_size_percent
                    symbol_height = cell_height * self._symbol_size_percent
                    
                    # 计算符号所在的矩形区域
                    symbol_rect = QRectF(
                        center_x - symbol_width/2,
                        center_y - symbol_height/2,
                        symbol_width,
                        symbol_height
                    )
                    
                    # 根据值的类型绘制不同的符号
                    if value == 'X':
                        self._draw_x_symbol(
                            painter, 
                            symbol_rect, 
                            is_highlighted=is_last_column and self._highlight_last_column
                        )
                    elif value == 'O':
                        self._draw_o_symbol(
                            painter, 
                            symbol_rect, 
                            is_highlighted=is_last_column and self._highlight_last_column
                        )
        
        # 恢复画笔状态
        painter.restore()
    
    def _draw_x_symbol(self, painter: QPainter, rect: QRectF, is_highlighted: bool = False) -> None:
        """
        绘制X符号
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRectF - 符号所在的矩形区域
            is_highlighted: bool - 是否高亮显示
        """
        # 设置画笔
        pen = QPen()
        pen.setWidth(self._x_width)
        
        # 如果需要高亮显示最后一列
        if is_highlighted:
            # 先绘制高亮背景
            highlight_rect = QRectF(
                rect.x() - 2,
                rect.y() - 2,
                rect.width() + 4,
                rect.height() + 4
            )
            highlight_color = QColor(self._last_column_highlight_color)
            highlight_color.setAlpha(80)  # 设置透明度
            painter.fillRect(highlight_rect, highlight_color)
            
            # 使用加粗的线条
            pen.setWidth(self._x_width + 1)
        
        pen.setColor(QColor(self._x_color))
        painter.setPen(pen)
        
        # 绘制X符号（两条交叉的线）
        painter.drawLine(
            rect.left(), rect.top(),
            rect.right(), rect.bottom()
        )
        painter.drawLine(
            rect.right(), rect.top(),
            rect.left(), rect.bottom()
        )
        
        # 如果需要填充
        if self._filled_symbols:
            # 使用半透明填充
            fill_color = QColor(self._x_color)
            fill_color.setAlpha(40)  # 设置透明度
            painter.fillRect(rect, fill_color)
    
    def _draw_o_symbol(self, painter: QPainter, rect: QRectF, is_highlighted: bool = False) -> None:
        """
        绘制O符号
        
        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRectF - 符号所在的矩形区域
            is_highlighted: bool - 是否高亮显示
        """
        # 设置画笔
        pen = QPen()
        pen.setWidth(self._o_width)
        
        # 如果需要高亮显示最后一列
        if is_highlighted:
            # 先绘制高亮背景
            highlight_rect = QRectF(
                rect.x() - 2,
                rect.y() - 2,
                rect.width() + 4,
                rect.height() + 4
            )
            highlight_color = QColor(self._last_column_highlight_color)
            highlight_color.setAlpha(80)  # 设置透明度
            painter.fillRect(highlight_rect, highlight_color)
            
            # 使用加粗的线条
            pen.setWidth(self._o_width + 1)
        
        pen.setColor(QColor(self._o_color))
        painter.setPen(pen)
        
        # 绘制O符号（圆形）
        painter.drawEllipse(rect)
        
        # 如果需要填充
        if self._filled_symbols:
            # 使用半透明填充
            fill_color = QColor(self._o_color)
            fill_color.setAlpha(40)  # 设置透明度
            
            # 保存画笔状态
            painter.save()
            
            # 设置无边框填充
            painter.setPen(Qt.NoPen)
            painter.setBrush(QBrush(fill_color))
            
            # 绘制填充圆形
            painter.drawEllipse(rect)
            
            # 恢复画笔状态
            painter.restore()

    def _build_view_info(self, chart_model: Dict[str, Any], cell_width: int, cell_height: int) -> Dict[str, Any]:
        """
        根据图表模型和单元格尺寸构建视图信息

        参数:
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度

        返回:
            Dict[str, Any] - 视图信息字典
        """
        view_info = {}

        # 获取图表数据
        chart_data = chart_model.get('chart_data')
        price_data = chart_model.get('price_data', [])

        if chart_data is None:
            return view_info

        # 构建价格到Y坐标的映射
        price_to_y = {}
        for i, price in enumerate(price_data):
            price_to_y[price] = i * cell_height + cell_height // 2

        # 构建列索引到X坐标的映射
        col_to_x = {}
        num_cols = len(chart_data.columns) if hasattr(chart_data, 'columns') else 0
        for i in range(num_cols):
            col_to_x[i] = i * cell_width + cell_width // 2

        view_info.update({
            'chart_data': chart_data,
            'price_to_y': price_to_y,
            'col_to_x': col_to_x
        })

        return view_info

    def _adjust_for_cell_size(self, cell_width: int, cell_height: int) -> None:
        """
        根据单元格尺寸调整符号线宽

        参数:
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度
        """
        # 基础尺寸（20像素）
        base_size = 20

        # 计算缩放比例
        width_scale = cell_width / base_size
        height_scale = cell_height / base_size
        scale_factor = min(width_scale, height_scale)

        # 调整X符号线宽（最小为1像素，最大为5像素）
        base_x_width = 2
        self._x_width = max(1, min(5, int(base_x_width * scale_factor)))

        # 调整O符号线宽（最小为1像素，最大为5像素）
        base_o_width = 2
        self._o_width = max(1, min(5, int(base_o_width * scale_factor)))