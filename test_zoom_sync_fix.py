#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
威科夫点数图缩放同步修复测试脚本

测试OX符号与背景网格线的同步缩放功能
"""

import sys
import os
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.ui.point_figure.widgets.chart_widget import ChartWidget


class ZoomTestWindow(QMainWindow):
    """缩放测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("威科夫点数图缩放同步测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        control_panel = self._create_control_panel()
        layout.addWidget(control_panel)
        
        # 创建图表控件
        self.chart_widget = ChartWidget()
        layout.addWidget(self.chart_widget)
        
        # 生成测试数据
        self._generate_test_data()
        
        # 设置初始缩放
        self.current_zoom = 1.0
        self._update_zoom_display()
    
    def _create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # 缩放控制按钮
        zoom_out_btn = QPushButton("缩小 (0.8x)")
        zoom_out_btn.clicked.connect(lambda: self._apply_zoom(0.8))
        layout.addWidget(zoom_out_btn)
        
        zoom_in_btn = QPushButton("放大 (1.25x)")
        zoom_in_btn.clicked.connect(lambda: self._apply_zoom(1.25))
        layout.addWidget(zoom_in_btn)
        
        reset_btn = QPushButton("重置缩放")
        reset_btn.clicked.connect(self._reset_zoom)
        layout.addWidget(reset_btn)
        
        # 缩放显示标签
        self.zoom_label = QLabel("当前缩放: 100%")
        layout.addWidget(self.zoom_label)
        
        layout.addStretch()
        
        # 测试说明
        info_label = QLabel("测试说明: 观察缩放时OX符号与网格线是否同步变化")
        info_label.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(info_label)
        
        return panel
    
    def _generate_test_data(self):
        """生成测试用的点数图数据"""
        # 创建价格序列
        prices = np.arange(10.0, 30.1, 0.5)  # 从10.0到30.0，步长0.5
        
        # 创建列数据（模拟时间序列）
        num_cols = 20
        
        # 创建DataFrame
        chart_data = pd.DataFrame(index=prices, columns=range(num_cols))
        
        # 填充一些测试数据
        np.random.seed(42)  # 确保可重现的结果
        
        for col in range(num_cols):
            # 随机选择一些价格点填入X或O
            num_points = np.random.randint(3, 8)  # 每列3-7个点
            selected_prices = np.random.choice(prices, num_points, replace=False)
            
            for price in selected_prices:
                # 随机选择X或O
                symbol = np.random.choice(['X', 'O'])
                chart_data.loc[price, col] = symbol
        
        # 构建图表模型
        chart_model = {
            'chart_data': chart_data,
            'price_data': prices.tolist(),
            'column_data': list(range(num_cols)),
            'date_labels': [f"Day {i+1}" for i in range(num_cols)],
            'trendlines': [],
            'support_resistance_levels': [],
            'selected_trendline_index': -1,
            'extend_trendlines': False
        }
        
        # 设置图表数据
        self.chart_widget.set_chart_model(chart_model)
    
    def _apply_zoom(self, factor):
        """应用缩放"""
        self.current_zoom *= factor
        
        # 限制缩放范围
        self.current_zoom = max(0.1, min(5.0, self.current_zoom))
        
        # 应用到图表控件
        zoom_controller = self.chart_widget._controller.zoom_controller
        zoom_controller.zoom_factor = self.current_zoom
        
        # 更新显示
        self._update_zoom_display()
        
        # 强制重绘
        self.chart_widget.update()
        
        print(f"应用缩放: {self.current_zoom:.2f}x ({self.current_zoom*100:.0f}%)")
    
    def _reset_zoom(self):
        """重置缩放"""
        self.current_zoom = 1.0
        
        # 重置图表控件
        zoom_controller = self.chart_widget._controller.zoom_controller
        zoom_controller.zoom_factor = 1.0
        
        # 更新显示
        self._update_zoom_display()
        
        # 强制重绘
        self.chart_widget.update()
        
        print("重置缩放到100%")
    
    def _update_zoom_display(self):
        """更新缩放显示"""
        self.zoom_label.setText(f"当前缩放: {self.current_zoom*100:.0f}%")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ZoomTestWindow()
    window.show()
    
    print("威科夫点数图缩放同步测试启动")
    print("=" * 50)
    print("测试说明:")
    print("1. 点击'放大'和'缩小'按钮测试缩放功能")
    print("2. 观察OX符号与背景网格线是否同步缩放")
    print("3. 检查符号线宽是否随缩放调整")
    print("4. 验证价格标签字体大小是否同步变化")
    print("=" * 50)
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
