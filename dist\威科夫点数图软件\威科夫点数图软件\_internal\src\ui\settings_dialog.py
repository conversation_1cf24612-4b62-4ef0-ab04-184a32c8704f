#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton,
                            QGroupBox, QDoubleSpinBox, QSpinBox, QDialogButtonBox)
from PyQt5.QtCore import Qt

from utils.settings_manager import SettingsManager


class SettingsDialog(QDialog):
    """设置对话框类"""

    def __init__(self, parent=None):
        """初始化设置对话框"""
        super().__init__(parent)

        # 获取设置管理器
        self.settings = SettingsManager()

        # 初始化界面
        self._init_ui()

        # 加载当前设置
        self._load_settings()

    def _init_ui(self):
        """初始化UI"""
        # 设置窗口属性
        self.setWindowTitle("设置")
        self.setMinimumWidth(400)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建点数图设置组
        chart_group = QGroupBox("点数图设置")
        chart_layout = QFormLayout(chart_group)

        # 格子大小
        self.box_size_spin = QDoubleSpinBox()
        self.box_size_spin.setRange(0.01, 1000.0)
        self.box_size_spin.setSingleStep(0.01)
        self.box_size_spin.setDecimals(2)
        chart_layout.addRow("格子大小:", self.box_size_spin)

        # 反转数量
        self.reversal_spin = QSpinBox()
        self.reversal_spin.setRange(1, 10)
        chart_layout.addRow("反转数量:", self.reversal_spin)

        # 计算方法
        self.method_combo = QComboBox()
        self.method_combo.addItems(["高低价法", "收盘价法"])
        chart_layout.addRow("计算方法:", self.method_combo)

        # 添加点数图设置组到主布局
        main_layout.addWidget(chart_group)

        # 创建表格预扩展设置组
        expansion_group = QGroupBox("表格预扩展设置")
        expansion_layout = QFormLayout(expansion_group)

        # 向上扩展行数
        self.expand_up_spin = QSpinBox()
        self.expand_up_spin.setRange(0, 50)
        self.expand_up_spin.setToolTip("在当前价格范围上方预扩展的行数")
        expansion_layout.addRow("向上扩展行数:", self.expand_up_spin)

        # 向下扩展行数
        self.expand_down_spin = QSpinBox()
        self.expand_down_spin.setRange(0, 50)
        self.expand_down_spin.setToolTip("在当前价格范围下方预扩展的行数")
        expansion_layout.addRow("向下扩展行数:", self.expand_down_spin)

        # 向右扩展列数
        self.expand_right_spin = QSpinBox()
        self.expand_right_spin.setRange(0, 50)
        self.expand_right_spin.setToolTip("在最新列右侧预扩展的列数")
        expansion_layout.addRow("向右扩展列数:", self.expand_right_spin)

        # 添加表格预扩展设置组到主布局
        main_layout.addWidget(expansion_group)

        # 创建Web视图设置组
        web_group = QGroupBox("Web视图设置")
        web_layout = QFormLayout(web_group)

        # 缩放级别
        self.zoom_spin = QSpinBox()
        self.zoom_spin.setRange(50, 200)
        self.zoom_spin.setSingleStep(10)
        self.zoom_spin.setSuffix("%")
        web_layout.addRow("默认缩放级别:", self.zoom_spin)

        # 添加Web视图设置组到主布局
        main_layout.addWidget(web_group)

        # 创建按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # 添加按钮到主布局
        main_layout.addWidget(button_box)

    def _load_settings(self):
        """加载当前设置"""
        # 加载点数图设置
        self.box_size_spin.setValue(self.settings.get_setting("chart", "box_size", 0.1))
        self.reversal_spin.setValue(self.settings.get_setting("chart", "reversal_amount", 3))

        # 设置计算方法
        method = self.settings.get_setting("chart", "method", "high_low")
        method_index = 0  # 默认为高低价法
        if method == "close":
            method_index = 1
        self.method_combo.setCurrentIndex(method_index)

        # 加载表格预扩展设置
        self.expand_up_spin.setValue(self.settings.get_setting("table_expansion", "expand_up_rows", 10))
        self.expand_down_spin.setValue(self.settings.get_setting("table_expansion", "expand_down_rows", 10))
        self.expand_right_spin.setValue(self.settings.get_setting("table_expansion", "expand_right_columns", 10))

        # 加载Web视图设置
        self.zoom_spin.setValue(self.settings.get_setting("web_view", "zoom_level", 100))

    def accept(self):
        """确认按钮点击事件"""
        # 保存点数图设置
        self.settings.set_setting("chart", "box_size", self.box_size_spin.value())
        self.settings.set_setting("chart", "reversal_amount", self.reversal_spin.value())

        # 保存计算方法
        method_index = self.method_combo.currentIndex()
        method = "high_low"  # 默认为高低价法
        if method_index == 1:
            method = "close"
        self.settings.set_setting("chart", "method", method)

        # 保存表格预扩展设置
        self.settings.set_setting("table_expansion", "expand_up_rows", self.expand_up_spin.value())
        self.settings.set_setting("table_expansion", "expand_down_rows", self.expand_down_spin.value())
        self.settings.set_setting("table_expansion", "expand_right_columns", self.expand_right_spin.value())

        # 保存Web视图设置
        self.settings.set_setting("web_view", "zoom_level", self.zoom_spin.value())

        # 保存设置
        self.settings.save_settings()

        # 调用父类的accept方法关闭对话框
        super().accept()