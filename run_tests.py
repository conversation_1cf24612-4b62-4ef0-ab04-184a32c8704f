#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试运行脚本

运行所有测试并生成覆盖率报告
"""

import os
import sys
import pytest

def main():
    """运行测试并生成覆盖率报告"""
    print("开始运行测试...")
    
    # 设置测试参数
    test_args = [
        "-v",  # 详细输出
        "--cov=src",  # 覆盖率分析目标目录
        "--cov-report=term",  # 输出到终端
        "--cov-report=html:tests/coverage_html",  # 输出HTML报告
        "tests/"  # 测试目录
    ]
    
    # 运行测试
    result = pytest.main(test_args)
    
    # 打印结果
    if result == 0:
        print("\n✅ 所有测试通过")
    else:
        print(f"\n❌ 测试失败，退出代码: {result}")
    
    # 打印覆盖率报告路径
    if os.path.exists("tests/coverage_html"):
        print("\n覆盖率报告已生成，可以在以下路径查看详细结果：")
        print(f"  file://{os.path.abspath('tests/coverage_html/index.html')}")
    
    return result

if __name__ == "__main__":
    sys.exit(main()) 