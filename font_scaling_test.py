#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体缩放修复验证测试
测试修复后的字体缩放逻辑是否能解决图片中显示的问题
"""

import sys
import os
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QTableWidget, QTableWidgetItem, QSlider, 
                             QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QBrush, QColor

class FontScalingTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("字体缩放修复验证测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化变量
        self.zoom_level = 100
        self.base_size = 20
        
        self.init_ui()
        self.create_test_data()
        self.apply_zoom()
        
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        # 缩放滑块
        control_layout.addWidget(QLabel("缩放级别:"))
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setMinimum(5)  # 5%
        self.zoom_slider.setMaximum(200)  # 200%
        self.zoom_slider.setValue(100)
        self.zoom_slider.valueChanged.connect(self.on_zoom_changed)
        control_layout.addWidget(self.zoom_slider)
        
        self.zoom_label = QLabel("100%")
        control_layout.addWidget(self.zoom_label)
        
        # 重置按钮
        reset_btn = QPushButton("重置到100%")
        reset_btn.clicked.connect(self.reset_zoom)
        control_layout.addWidget(reset_btn)
        
        layout.addLayout(control_layout)
        
        # 测试表格
        self.test_table = QTableWidget()
        layout.addWidget(self.test_table)
        
        # 日志输出
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        layout.addWidget(self.log_text)
        
    def create_test_data(self):
        """创建测试数据"""
        # 创建模拟的点数图数据
        rows = 20
        cols = 15
        
        self.test_table.setRowCount(rows)
        self.test_table.setColumnCount(cols)
        
        # 设置表头
        for col in range(cols):
            self.test_table.setHorizontalHeaderItem(col, QTableWidgetItem(f"列{col+1}"))
        
        for row in range(rows):
            self.test_table.setVerticalHeaderItem(row, QTableWidgetItem(f"{100-row*0.5:.1f}"))
        
        # 填充测试数据（模拟X和O符号）
        import random
        for row in range(rows):
            for col in range(cols):
                if random.random() < 0.3:  # 30%概率放置符号
                    symbol = 'X' if random.random() < 0.5 else 'O'
                    item = QTableWidgetItem(symbol)
                    
                    # 设置颜色
                    if symbol == 'X':
                        item.setForeground(QBrush(QColor(255, 0, 0)))  # 红色
                    else:
                        item.setForeground(QBrush(QColor(0, 0, 255)))  # 蓝色
                    
                    item.setTextAlignment(Qt.AlignCenter)
                    self.test_table.setItem(row, col, item)
                else:
                    # 空单元格
                    item = QTableWidgetItem("")
                    self.test_table.setItem(row, col, item)
        
        self._log_result("测试数据创建完成")
        
    def on_zoom_changed(self, value):
        """缩放级别改变时的处理"""
        self.zoom_level = value
        self.zoom_label.setText(f"{value}%")
        self.apply_zoom()
        
    def reset_zoom(self):
        """重置缩放到100%"""
        self.zoom_slider.setValue(100)
        
    def apply_zoom(self):
        """应用缩放 - 使用修复后的逻辑"""
        try:
            # 计算单元格大小
            cell_size = max(1, int(self.base_size * self.zoom_level / 100))
            
            # 设置列宽和行高
            for col in range(self.test_table.columnCount()):
                self.test_table.setColumnWidth(col, cell_size)
            
            for row in range(self.test_table.rowCount()):
                self.test_table.setRowHeight(row, cell_size)
            
            # 应用智能字体缩放（修复后的逻辑）
            font_size = self._calculate_smart_font_size()
            
            # 应用字体到所有符号
            for row in range(self.test_table.rowCount()):
                for col in range(self.test_table.columnCount()):
                    item = self.test_table.item(row, col)
                    if item and item.text() in ['X', 'O']:
                        font = QFont()
                        font.setBold(True)
                        font.setPointSize(font_size)
                        item.setFont(font)
            
            # 调整网格线样式
            self._adjust_grid_style()
            
            # 记录日志
            scale_factor = self.zoom_level / 100.0
            self._log_result(f"缩放应用: {self.zoom_level}% | 单元格: {cell_size}px | 字体: {font_size}pt | 缩放因子: {scale_factor:.3f}")
            
        except Exception as e:
            self._log_result(f"缩放应用失败: {e}")
    
    def _calculate_smart_font_size(self):
        """
        计算智能字体大小 - 修复后的逻辑
        这是解决图片中问题的核心算法
        """
        scale_factor = self.zoom_level / 100.0
        
        if scale_factor >= 0.5:
            # 正常缩放：字体按比例缩放
            font_size = max(6, int(8 * scale_factor))
        elif scale_factor >= 0.25:
            # 中等缩小：保持较小但可读的字体
            font_size = max(4, int(6 * scale_factor))
        elif scale_factor >= 0.1:
            # 小缩放：使用最小可读字体
            font_size = max(3, int(4 * scale_factor))
        else:
            # 极小缩放：使用绝对最小字体
            font_size = max(2, int(3 * scale_factor))
        
        return font_size
    
    def _adjust_grid_style(self):
        """调整网格线样式 - 修复后的逻辑"""
        scale_factor = self.zoom_level / 100.0
        
        if scale_factor >= 0.5:
            # 正常缩放：使用标准网格线
            grid_style = "QTableWidget { gridline-color: #D0D0D0; }"
        elif scale_factor >= 0.25:
            # 中等缩小：使用较细的网格线
            grid_style = "QTableWidget { gridline-color: rgba(208, 208, 208, 180); }"
        else:
            # 极小缩放：使用最细的网格线，增加透明度
            alpha = max(64, int(128 * scale_factor / 0.25))
            grid_style = f"QTableWidget {{ gridline-color: rgba(208, 208, 208, {alpha}); }}"
        
        self.test_table.setStyleSheet(grid_style)
    
    def _log_result(self, message):
        """记录日志"""
        self.log_text.append(message)
        print(message)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("字体缩放修复验证测试")
    app.setApplicationVersion("1.0")
    
    window = FontScalingTestWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
