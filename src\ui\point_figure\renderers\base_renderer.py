#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图基础渲染器接口

定义所有渲染器必须实现的接口方法
"""

from typing import Dict, Any, Optional
from PyQt5.QtGui import QPainter
from PyQt5.QtCore import QRect


class BaseRenderer:
    """
    渲染器基类接口
    
    所有渲染器必须继承此类并实现render方法
    """
    
    def __init__(self):
        """初始化渲染器"""
        self._visible = True
        self._z_index = 0  # 控制渲染顺序，较小的值先渲染（背景层）
    
    @property
    def visible(self) -> bool:
        """渲染器是否可见"""
        return self._visible
    
    @visible.setter
    def visible(self, value: bool):
        """设置渲染器可见性"""
        self._visible = value
    
    @property
    def z_index(self) -> int:
        """获取渲染层级"""
        return self._z_index
    
    @z_index.setter
    def z_index(self, value: int):
        """设置渲染层级"""
        self._z_index = value
    
    def render(self, painter: QPainter, rect: QRect, chart_model: Dict[str, Any],
               cell_width: int, cell_height: int) -> None:
        """
        执行渲染操作

        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度（像素）
            cell_height: int - 单元格高度（像素）
        """
        raise NotImplementedError("子类必须实现render方法")