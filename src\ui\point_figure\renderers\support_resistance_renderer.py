#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图支撑阻力渲染器

负责绘制点数图的支撑位和阻力位
"""

from typing import Dict, Any, List, Tuple
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import QRect, Qt, QPoint, QPointF, QLineF

from .base_renderer import BaseRenderer


class SupportResistanceRenderer(BaseRenderer):
    """
    支撑阻力渲染器
    
    负责绘制点数图的支撑位和阻力位
    """
    
    def __init__(self):
        """初始化支撑阻力渲染器"""
        super().__init__()
        self._z_index = 5  # 在符号之上但在趋势线之下
        self._support_color = "#00AA00"  # 支撑位颜色
        self._resistance_color = "#CC0000"  # 阻力位颜色
        self._mixed_color = "#AA6600"  # 支撑/阻力位颜色
        self._line_width = 1.5  # 线宽
        self._line_style = Qt.DashLine  # 线型
        self._show_labels = True  # 是否显示标签
        self._label_font_size = 8  # 标签字体大小
        self._weak_alpha = 60  # 弱强度透明度
        self._medium_alpha = 120  # 中等强度透明度
        self._strong_alpha = 180  # 强强度透明度
    
    @property
    def show_labels(self) -> bool:
        """是否显示标签"""
        return self._show_labels
    
    @show_labels.setter
    def show_labels(self, value: bool):
        """设置是否显示标签"""
        self._show_labels = value
    
    @property
    def support_color(self) -> str:
        """获取支撑位颜色"""
        return self._support_color
    
    @support_color.setter
    def support_color(self, value: str):
        """设置支撑位颜色"""
        self._support_color = value
    
    @property
    def resistance_color(self) -> str:
        """获取阻力位颜色"""
        return self._resistance_color
    
    @resistance_color.setter
    def resistance_color(self, value: str):
        """设置阻力位颜色"""
        self._resistance_color = value
    
    def render(self, painter: QPainter, rect: QRect, chart_model: Dict[str, Any],
               cell_width: int, cell_height: int) -> None:
        """
        绘制支撑阻力位

        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度（像素）
            cell_height: int - 单元格高度（像素）
        """
        if not self.visible:
            return

        # 从图表模型中获取数据
        if not chart_model:
            return

        # 构建视图信息
        view_info = self._build_view_info(chart_model, cell_width, cell_height)

        # 获取必要的视图信息
        levels = view_info.get('support_resistance_levels', [])
        price_to_y = view_info.get('price_to_y', {})
        price_labels_width = view_info.get('price_labels_width', 60)
        chart_width = view_info.get('chart_width', rect.width())
        
        if not levels:
            return
        
        # 保存画笔状态
        painter.save()
        
        # 遍历并绘制每个支撑阻力位
        for level in levels:
            # 如果该级别未被选中显示，则跳过
            if not level.selected:
                continue
                
            # 获取价格对应的Y坐标
            if level.price not in price_to_y:
                continue
                
            y = price_to_y[level.price]
            
            # 绘制支撑阻力线
            self._draw_level_line(
                painter,
                y,
                level,
                price_labels_width,
                chart_width
            )
            
            # 绘制标签
            if self._show_labels:
                self._draw_level_label(
                    painter,
                    y,
                    level,
                    price_labels_width
                )
        
        # 恢复画笔状态
        painter.restore()
    
    def _draw_level_line(self, painter: QPainter, y: int, level: Any, 
                        price_labels_width: int, chart_width: int) -> None:
        """
        绘制单条支撑阻力线
        
        参数:
            painter: QPainter - 用于绘图的画笔
            y: int - 线条Y坐标
            level: Any - 支撑阻力位数据
            price_labels_width: int - 价格标签区域宽度
            chart_width: int - 图表宽度
        """
        # 设置画笔
        pen = QPen()
        pen.setWidth(self._line_width)
        pen.setStyle(self._line_style)
        
        # 根据类型设置颜色
        color = self._get_level_color(level.type)
        alpha = self._get_alpha_by_strength(level.strength)
        
        qcolor = QColor(color)
        qcolor.setAlpha(alpha)
        pen.setColor(qcolor)
        
        painter.setPen(pen)
        
        # 绘制水平线
        painter.drawLine(price_labels_width, y, chart_width, y)
    
    def _draw_level_label(self, painter: QPainter, y: int, level: Any, price_labels_width: int) -> None:
        """
        绘制支撑阻力位标签
        
        参数:
            painter: QPainter - 用于绘图的画笔
            y: int - 标签Y坐标
            level: Any - 支撑阻力位数据
            price_labels_width: int - 价格标签区域宽度
        """
        # 设置字体
        font = painter.font()
        font.setPointSize(self._label_font_size)
        painter.setFont(font)
        
        # 设置颜色
        color = self._get_level_color(level.type)
        painter.setPen(QColor(color))
        
        # 计算标签位置
        label_x = price_labels_width + 5
        label_y = y
        
        # 生成标签文本
        label_text = f"{level.type} ({level.strength}) - {level.price:.2f}"
        text_width = painter.fontMetrics().width(label_text)
        text_height = painter.fontMetrics().height()
        
        # 绘制标签背景
        background_rect = QRect(
            label_x - 2,
            label_y - text_height + 4,
            text_width + 4,
            text_height + 4
        )
        
        # 绘制半透明背景
        background_color = QColor(Qt.white)
        background_color.setAlpha(180)
        painter.fillRect(background_rect, background_color)
        
        # 绘制标签文本
        painter.drawText(label_x, label_y, label_text)
    
    def _get_level_color(self, level_type: str) -> str:
        """
        根据支撑阻力位类型获取颜色
        
        参数:
            level_type: str - 类型："支撑位"、"阻力位"或"支撑/阻力"
            
        返回:
            str - 对应的颜色代码
        """
        if level_type == "支撑位":
            return self._support_color
        elif level_type == "阻力位":
            return self._resistance_color
        else:  # "支撑/阻力"
            return self._mixed_color
    
    def _get_alpha_by_strength(self, strength: str) -> int:
        """
        根据强度获取透明度
        
        参数:
            strength: str - 强度："弱"、"中"或"强"
            
        返回:
            int - 透明度值(0-255)
        """
        if strength == "弱":
            return self._weak_alpha
        elif strength == "中":
            return self._medium_alpha
        else:  # "强"
            return self._strong_alpha

    def _build_view_info(self, chart_model: Dict[str, Any], cell_width: int, cell_height: int) -> Dict[str, Any]:
        """
        根据图表模型和单元格尺寸构建视图信息

        参数:
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度

        返回:
            Dict[str, Any] - 视图信息字典
        """
        view_info = {}

        # 获取支撑阻力位数据
        levels = chart_model.get('support_resistance_levels', [])
        price_data = chart_model.get('price_data', [])
        column_data = chart_model.get('column_data', [])

        # 构建价格到Y坐标的映射
        price_to_y = {}
        for i, price in enumerate(price_data):
            price_to_y[price] = i * cell_height + cell_height // 2

        # 计算价格标签区域宽度（根据单元格尺寸调整）
        base_label_width = 60
        base_size = 20
        scale_factor = min(cell_width / base_size, cell_height / base_size)
        price_labels_width = max(40, int(base_label_width * scale_factor))

        # 计算图表宽度
        chart_width = len(column_data) * cell_width

        view_info.update({
            'support_resistance_levels': levels,
            'price_to_y': price_to_y,
            'price_labels_width': price_labels_width,
            'chart_width': chart_width
        })

        return view_info