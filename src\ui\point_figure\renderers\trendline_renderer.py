#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图趋势线渲染器

负责绘制点数图的各种趋势线
"""

from typing import Dict, Any, List, Tuple
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import QRect, Qt, QPoint, QPointF, QLineF

from .base_renderer import BaseRenderer


class TrendlineRenderer(BaseRenderer):
    """
    趋势线渲染器
    
    负责绘制点数图的各种趋势线
    """
    
    def __init__(self):
        """初始化趋势线渲染器"""
        super().__init__()
        self._z_index = 10  # 在符号之上绘制趋势线
        self._selected_trendline_width = 2.0  # 当前选中趋势线的宽度
        self._default_trendline_width = 1.5  # 默认趋势线宽度
        self._selected_trendline_color = "#FF9900"  # 选中趋势线的颜色
        self._extension_line_style = Qt.DashLine  # 趋势线延长线样式
        self._show_trendline_labels = True  # 是否显示趋势线标签
    
    @property
    def show_trendline_labels(self) -> bool:
        """是否显示趋势线标签"""
        return self._show_trendline_labels
    
    @show_trendline_labels.setter
    def show_trendline_labels(self, value: bool):
        """设置是否显示趋势线标签"""
        self._show_trendline_labels = value
    
    def render(self, painter: QPainter, rect: QRect, chart_model: Dict[str, Any],
               cell_width: int, cell_height: int) -> None:
        """
        绘制趋势线

        参数:
            painter: QPainter - 用于绘图的画笔
            rect: QRect - 绘图区域
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度（像素）
            cell_height: int - 单元格高度（像素）
        """
        if not self.visible:
            return

        # 从图表模型中获取数据
        if not chart_model:
            return

        # 构建视图信息
        view_info = self._build_view_info(chart_model, cell_width, cell_height)

        # 获取必要的视图信息
        trendlines = view_info.get('trendlines', [])
        price_to_y = view_info.get('price_to_y', {})
        col_to_x = view_info.get('col_to_x', {})
        selected_trendline_index = view_info.get('selected_trendline_index', -1)
        extend_trendlines = view_info.get('extend_trendlines', False)
        chart_width = view_info.get('chart_width', rect.width())
        chart_height = view_info.get('chart_height', rect.height())

        if not trendlines:
            return
        
        # 保存画笔状态
        painter.save()
        
        # 遍历并绘制每条趋势线
        for idx, trendline in enumerate(trendlines):
            # 确定是否为选中的趋势线
            is_selected = (idx == selected_trendline_index)
            
            # 尝试获取起始和结束点的坐标
            start_x = col_to_x.get(trendline.start_col)
            start_y = price_to_y.get(trendline.start_price)
            end_x = col_to_x.get(trendline.end_col)
            end_y = price_to_y.get(trendline.end_price)
            
            # 如果坐标无效，跳过该趋势线
            if start_x is None or start_y is None or end_x is None or end_y is None:
                continue
            
            # 创建线条对象
            line = QLineF(start_x, start_y, end_x, end_y)
            
            # 绘制趋势线
            self._draw_trendline(
                painter, 
                line, 
                trendline, 
                is_selected, 
                extend_trendlines,
                chart_width,
                chart_height
            )
            
            # 绘制趋势线标签
            if self._show_trendline_labels:
                self._draw_trendline_label(
                    painter,
                    line,
                    trendline,
                    is_selected
                )
        
        # 恢复画笔状态
        painter.restore()
    
    def _draw_trendline(self, painter: QPainter, line: QLineF, trendline: Any, 
                       is_selected: bool, extend: bool, chart_width: int, chart_height: int) -> None:
        """
        绘制单条趋势线
        
        参数:
            painter: QPainter - 用于绘图的画笔
            line: QLineF - 线条对象
            trendline: Any - 趋势线数据
            is_selected: bool - 是否为选中的趋势线
            extend: bool - 是否延长趋势线
            chart_width: int - 图表宽度
            chart_height: int - 图表高度
        """
        # 设置画笔
        pen = QPen()
        
        # 确定线宽
        if is_selected:
            pen.setWidth(self._selected_trendline_width)
            pen.setColor(QColor(self._selected_trendline_color))
        else:
            pen.setWidth(self._default_trendline_width)
            pen.setColor(QColor(trendline.color))
        
        # 设置线型
        line_style = self._get_line_style(trendline.style)
        pen.setStyle(line_style)
        
        painter.setPen(pen)
        
        # 绘制基本趋势线
        painter.drawLine(line)
        
        # 如果需要延长趋势线
        if extend:
            # 计算并绘制延长线
            self._draw_extension_line(
                painter, 
                line, 
                trendline, 
                chart_width, 
                chart_height
            )
    
    def _draw_extension_line(self, painter: QPainter, line: QLineF, 
                            trendline: Any, chart_width: int, chart_height: int) -> None:
        """
        绘制趋势线的延长线
        
        参数:
            painter: QPainter - 用于绘图的画笔
            line: QLineF - 原始线条对象
            trendline: Any - 趋势线数据
            chart_width: int - 图表宽度
            chart_height: int - 图表高度
        """
        # 保存当前画笔
        original_pen = painter.pen()
        
        # 创建新画笔
        extension_pen = QPen(original_pen)
        extension_pen.setStyle(self._extension_line_style)
        painter.setPen(extension_pen)
        
        # 计算趋势线的斜率
        dx = line.x2() - line.x1()
        dy = line.y2() - line.y1()
        
        # 防止除以零错误
        if dx == 0:
            return
        
        slope = dy / dx
        
        # 计算延长线的起点和终点
        if dx > 0:  # 向右延伸
            extension_start_x = line.x2()
            extension_start_y = line.y2()
            extension_end_x = chart_width
            extension_end_y = extension_start_y + slope * (extension_end_x - extension_start_x)
        else:  # 向左延伸
            extension_start_x = line.x1()
            extension_start_y = line.y1()
            extension_end_x = 0
            extension_end_y = extension_start_y + slope * (extension_end_x - extension_start_x)
        
        # 确保终点在图表范围内
        if extension_end_y < 0:
            # 计算与顶部边界的交点
            extension_end_y = 0
            extension_end_x = extension_start_x + (extension_end_y - extension_start_y) / slope
        elif extension_end_y > chart_height:
            # 计算与底部边界的交点
            extension_end_y = chart_height
            extension_end_x = extension_start_x + (extension_end_y - extension_start_y) / slope
        
        # 绘制延长线
        extension_line = QLineF(
            extension_start_x, extension_start_y,
            extension_end_x, extension_end_y
        )
        painter.drawLine(extension_line)
        
        # 恢复原始画笔
        painter.setPen(original_pen)
    
    def _draw_trendline_label(self, painter: QPainter, line: QLineF, 
                             trendline: Any, is_selected: bool) -> None:
        """
        绘制趋势线标签
        
        参数:
            painter: QPainter - 用于绘图的画笔
            line: QLineF - 线条对象
            trendline: Any - 趋势线数据
            is_selected: bool - 是否为选中的趋势线
        """
        # 计算标签位置（趋势线中点）
        label_x = (line.x1() + line.x2()) / 2
        label_y = (line.y1() + line.y2()) / 2
        
        # 设置字体
        font = painter.font()
        font.setBold(is_selected)  # 选中的趋势线使用粗体
        painter.setFont(font)
        
        # 设置颜色
        if is_selected:
            painter.setPen(QColor(self._selected_trendline_color))
        else:
            painter.setPen(QColor(trendline.color))
        
        # 绘制标签背景
        label_text = trendline.name
        text_width = painter.fontMetrics().width(label_text)
        text_height = painter.fontMetrics().height()
        
        background_rect = QRect(
            int(label_x - text_width/2 - 2),
            int(label_y - text_height/2 - 2),
            text_width + 4,
            text_height + 4
        )
        
        # 绘制半透明背景
        background_color = QColor(Qt.white)
        background_color.setAlpha(180)
        painter.fillRect(background_rect, background_color)
        
        # 绘制标签文本
        painter.drawText(
            int(label_x - text_width/2),
            int(label_y + text_height/3),  # 垂直居中的调整
            label_text
        )
    
    def _get_line_style(self, style_name: str) -> Qt.PenStyle:
        """
        根据样式名称获取Qt线型
        
        参数:
            style_name: str - 线型名称
            
        返回:
            Qt.PenStyle - Qt线型枚举值
        """
        style_map = {
            "solid": Qt.SolidLine,
            "dash": Qt.DashLine,
            "dot": Qt.DotLine,
            "dashdot": Qt.DashDotLine,
            "dashdotdot": Qt.DashDotDotLine
        }
        
        return style_map.get(style_name.lower(), Qt.SolidLine)

    def _build_view_info(self, chart_model: Dict[str, Any], cell_width: int, cell_height: int) -> Dict[str, Any]:
        """
        根据图表模型和单元格尺寸构建视图信息

        参数:
            chart_model: Dict[str, Any] - 图表数据模型
            cell_width: int - 单元格宽度
            cell_height: int - 单元格高度

        返回:
            Dict[str, Any] - 视图信息字典
        """
        view_info = {}

        # 获取趋势线数据
        trendlines = chart_model.get('trendlines', [])
        price_data = chart_model.get('price_data', [])
        column_data = chart_model.get('column_data', [])

        # 构建价格到Y坐标的映射
        price_to_y = {}
        for i, price in enumerate(price_data):
            price_to_y[price] = i * cell_height + cell_height // 2

        # 构建列索引到X坐标的映射
        col_to_x = {}
        for i in range(len(column_data)):
            col_to_x[i] = i * cell_width + cell_width // 2

        # 获取其他配置信息
        selected_trendline_index = chart_model.get('selected_trendline_index', -1)
        extend_trendlines = chart_model.get('extend_trendlines', False)
        chart_width = len(column_data) * cell_width
        chart_height = len(price_data) * cell_height

        view_info.update({
            'trendlines': trendlines,
            'price_to_y': price_to_y,
            'col_to_x': col_to_x,
            'selected_trendline_index': selected_trendline_index,
            'extend_trendlines': extend_trendlines,
            'chart_width': chart_width,
            'chart_height': chart_height
        })

        return view_info