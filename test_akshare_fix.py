#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试AkShare数据源修复效果
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

import pandas as pd
from datetime import datetime, timedelta
from services.data_sources.akshare_data_source import AkShareDataSource

def test_a_stock_data():
    """测试A股数据获取"""
    print("=" * 50)
    print("测试A股数据获取")
    print("=" * 50)
    
    # 创建AkShare数据源实例
    akshare_source = AkShareDataSource()
    
    # 测试参数
    stock_code = "000001"  # 平安银行
    market = "sz"
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    try:
        print(f"正在获取{market}.{stock_code}的数据...")
        data = akshare_source.get_stock_data(
            stock_code=stock_code,
            market=market,
            start_date=start_date,
            end_date=end_date,
            adjust_flag="qfq",
            frequency="daily"
        )
        
        if not data.empty:
            print(f"✅ 成功获取A股数据，共{len(data)}条记录")
            print("数据预览:")
            print(data.head())
            print(f"日期范围: {data['date'].min()} 到 {data['date'].max()}")
        else:
            print("❌ 未获取到A股数据")
            
    except Exception as e:
        print(f"❌ A股数据获取失败: {e}")

def test_us_stock_data():
    """测试美股数据获取"""
    print("\n" + "=" * 50)
    print("测试美股数据获取")
    print("=" * 50)
    
    # 创建AkShare数据源实例
    akshare_source = AkShareDataSource()
    
    # 测试参数
    stock_code = "AAPL"  # 苹果公司
    market = "us"
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    try:
        print(f"正在获取{market}.{stock_code}的数据...")
        data = akshare_source.get_stock_data(
            stock_code=stock_code,
            market=market,
            start_date=start_date,
            end_date=end_date,
            adjust_flag="",
            frequency="daily"
        )
        
        if not data.empty:
            print(f"✅ 成功获取美股数据，共{len(data)}条记录")
            print("数据预览:")
            print(data.head())
            print(f"日期范围: {data['date'].min()} 到 {data['date'].max()}")
        else:
            print("❌ 未获取到美股数据")
            
    except Exception as e:
        print(f"❌ 美股数据获取失败: {e}")

def test_enhanced_akshare():
    """测试增强版AkShare数据源"""
    print("\n" + "=" * 50)
    print("测试增强版AkShare数据源")
    print("=" * 50)
    
    try:
        from services.data_sources.enhanced_akshare_data_source import EnhancedAkShareDataSource
        
        # 创建增强版AkShare数据源实例
        enhanced_source = EnhancedAkShareDataSource()
        
        # 测试A股数据
        print("测试增强版A股数据获取...")
        data = enhanced_source.get_stock_data(
            stock_code="600000",
            market="sh",
            start_date="2024-01-01",
            end_date="2024-01-31",
            adjust_flag="qfq",
            frequency="daily"
        )
        
        if not data.empty:
            print(f"✅ 增强版A股数据获取成功，共{len(data)}条记录")
            print("数据预览:")
            print(data.head())
        else:
            print("❌ 增强版A股数据获取失败")
            
    except Exception as e:
        print(f"❌ 增强版AkShare测试失败: {e}")

def main():
    """主函数"""
    print("AkShare数据源修复测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试A股数据
    test_a_stock_data()
    
    # 测试美股数据
    test_us_stock_data()
    
    # 测试增强版AkShare
    test_enhanced_akshare()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
