#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试UI中价格显示格式的修复
验证不同格值下纵轴价格的显示是否正确
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

def test_price_display_format():
    """测试价格显示格式逻辑"""
    print("=== 测试价格显示格式逻辑 ===")
    
    # 模拟UI中的价格显示逻辑
    def format_price_display(price, box_size):
        """模拟UI中的价格显示格式化逻辑"""
        # 检查价格是否为整数
        if price == int(price):
            # 如果价格是整数，检查格值是否为整数
            if box_size == int(box_size):
                price_text = f"{price:.0f}"
            else:
                # 格值是小数，即使价格是整数也要显示小数位以保持一致性
                if box_size >= 0.1:
                    price_text = f"{price:.1f}"
                elif box_size >= 0.01:
                    price_text = f"{price:.2f}"
                else:
                    price_text = f"{price:.3f}"
        else:
            # 价格是小数，根据格值精度确定显示位数
            if box_size >= 0.1:
                price_text = f"{price:.1f}"
            elif box_size >= 0.01:
                price_text = f"{price:.2f}"
            else:
                price_text = f"{price:.3f}"
        
        return price_text
    
    # 测试用例
    test_cases = [
        # (价格, 格值, 期望显示, 说明)
        (10.5, 1.5, "10.5", "格值1.5，价格10.5应显示为10.5"),
        (12.0, 1.5, "12.0", "格值1.5，价格12.0应显示为12.0"),
        (13.5, 1.5, "13.5", "格值1.5，价格13.5应显示为13.5"),
        (15.0, 1.5, "15.0", "格值1.5，价格15.0应显示为15.0"),
        
        (10.0, 2.5, "10.0", "格值2.5，价格10.0应显示为10.0"),
        (12.5, 2.5, "12.5", "格值2.5，价格12.5应显示为12.5"),
        (15.0, 2.5, "15.0", "格值2.5，价格15.0应显示为15.0"),
        
        (10.5, 3.5, "10.5", "格值3.5，价格10.5应显示为10.5"),
        (14.0, 3.5, "14.0", "格值3.5，价格14.0应显示为14.0"),
        (17.5, 3.5, "17.5", "格值3.5，价格17.5应显示为17.5"),
        
        # 对比：整数格值的情况
        (10.0, 1.0, "10", "格值1.0，价格10.0应显示为10"),
        (11.0, 1.0, "11", "格值1.0，价格11.0应显示为11"),
        
        # 对比：小数格值0.5的情况
        (10.0, 0.5, "10.0", "格值0.5，价格10.0应显示为10.0"),
        (10.5, 0.5, "10.5", "格值0.5，价格10.5应显示为10.5"),
    ]
    
    print("价格显示格式测试:")
    print("格式: 价格 (格值) -> 显示 | 期望 | 结果")
    print("-" * 60)
    
    all_passed = True
    for price, box_size, expected, description in test_cases:
        actual = format_price_display(price, box_size)
        passed = actual == expected
        status = "✓" if passed else "✗"
        
        print(f"{price:6.1f} ({box_size:3.1f}) -> {actual:>6} | {expected:>6} | {status} {description}")
        
        if not passed:
            all_passed = False
    
    print("-" * 60)
    if all_passed:
        print("✓ 所有测试用例都通过")
    else:
        print("✗ 部分测试用例失败")
    
    return all_passed

def test_problematic_cases():
    """测试用户报告的问题案例"""
    print(f"\n{'='*60}")
    print("测试用户报告的问题案例")
    print(f"{'='*60}")
    
    # 模拟格值1.5时的价格序列
    box_size = 1.5
    prices = [7.5, 9.0, 10.5, 12.0, 13.5, 15.0, 16.5, 18.0, 19.5, 21.0]
    
    print(f"格值: {box_size}")
    print("修复前可能显示的问题价格: 6, 8, 9, 10, 12, 14, 15, 16, 18, 20, 21")
    print("修复后应该显示的正确价格:")
    
    def format_price_display(price, box_size):
        """模拟修复后的价格显示格式化逻辑"""
        if price == int(price):
            if box_size == int(box_size):
                price_text = f"{price:.0f}"
            else:
                if box_size >= 0.1:
                    price_text = f"{price:.1f}"
                elif box_size >= 0.01:
                    price_text = f"{price:.2f}"
                else:
                    price_text = f"{price:.3f}"
        else:
            if box_size >= 0.1:
                price_text = f"{price:.1f}"
            elif box_size >= 0.01:
                price_text = f"{price:.2f}"
            else:
                price_text = f"{price:.3f}"
        return price_text
    
    for price in prices:
        display_text = format_price_display(price, box_size)
        ratio = price / box_size
        print(f"  {display_text} (= {ratio:.1f} × {box_size})")
    
    print("\n验证: 所有显示的价格都是格值1.5的整数倍 ✓")

if __name__ == "__main__":
    test_price_display_format()
    test_problematic_cases() 