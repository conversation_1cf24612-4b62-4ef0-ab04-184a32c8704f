#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点数图视图控件

点数图的主视图控件，用于展示和交互
"""

from typing import Dict, Any, List, Optional, Tuple
from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QPainter, QPaintEvent, QResizeEvent, QColor
from PyQt5.QtCore import Qt, QEvent, QTimer, QRect, QPoint

import src.models.point_figure as models
from src.ui.point_figure.controllers.chart_controller import ChartController
from src.ui.point_figure.renderers.base_renderer import BaseRenderer
from src.ui.point_figure.renderers.grid_renderer import GridRenderer
from src.ui.point_figure.renderers.symbol_renderer import SymbolRenderer
from src.ui.point_figure.renderers.trendline_renderer import TrendlineRenderer
from src.ui.point_figure.renderers.support_resistance_renderer import SupportResistanceRenderer
from src.core.point_figure.data_structures import TargetPriceData


class ChartWidget(QWidget):
    """
    点数图视图控件
    
    提供点数图的可视化和交互功能
    """
    
    def __init__(self, parent=None):
        """
        初始化点数图视图控件
        
        参数:
            parent: QWidget - 父控件
        """
        super().__init__(parent)
        
        # 设置焦点和鼠标跟踪
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        
        # 初始化控制器
        self._controller = ChartController()
        self._controller.attach_view(self)
        
        # 初始化渲染器
        self._renderers: List[BaseRenderer] = []
        self._init_renderers()
        
        # 点数图数据模型
        self._chart_model = None
        
        # 视图参数
        self._cell_width = 20  # 每个单元格宽度（像素）
        self._cell_height = 20  # 每个单元格高度（像素）
        self._default_cell_width = 20  # 默认单元格宽度
        self._default_cell_height = 20  # 默认单元格高度
        self._show_tooltip = True  # 是否显示工具提示
        
        # 选择区域
        self._selection = None  # 当前选择的区域
        self._selection_start = None  # 选择开始位置
        self._selection_end = None  # 选择结束位置
        
        # 目标线数据
        self._target_lines = []  # 目标价格线列表
        
        # 用于双缓冲绘制的延迟更新定时器
        self._update_timer = QTimer(self)
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self.update)
    
    def _init_renderers(self) -> None:
        """初始化渲染器"""
        # 创建渲染器实例
        grid_renderer = GridRenderer()
        symbol_renderer = SymbolRenderer()
        trendline_renderer = TrendlineRenderer()
        support_resistance_renderer = SupportResistanceRenderer()
        
        # 添加到渲染器列表
        self._renderers.append(grid_renderer)
        self._renderers.append(symbol_renderer)
        self._renderers.append(trendline_renderer)
        self._renderers.append(support_resistance_renderer)
        
        # 按Z轴顺序排序渲染器
        self._sort_renderers()
    
    def _sort_renderers(self) -> None:
        """按Z轴顺序排序渲染器"""
        self._renderers.sort(key=lambda r: r.z_index)
    
    def set_chart_model(self, chart_model) -> None:
        """
        设置点数图数据模型
        
        参数:
            chart_model: 点数图数据模型
        """
        self._chart_model = chart_model
        
        # 清除选择区域和目标线
        self._selection = None
        self._selection_start = None
        self._selection_end = None
        self._target_lines = []
        
        self.update()
    
    def add_renderer(self, renderer: BaseRenderer) -> None:
        """
        添加渲染器
        
        参数:
            renderer: BaseRenderer - 要添加的渲染器
        """
        if renderer not in self._renderers:
            self._renderers.append(renderer)
            self._sort_renderers()
            self.update()
    
    def remove_renderer(self, renderer: BaseRenderer) -> None:
        """
        移除渲染器
        
        参数:
            renderer: BaseRenderer - 要移除的渲染器
        """
        if renderer in self._renderers:
            self._renderers.remove(renderer)
            self.update()
    
    def get_renderer(self, renderer_type) -> Optional[BaseRenderer]:
        """
        获取指定类型的渲染器
        
        参数:
            renderer_type: 渲染器类型
            
        返回:
            Optional[BaseRenderer] - 匹配的渲染器，如果不存在则返回None
        """
        for renderer in self._renderers:
            if isinstance(renderer, renderer_type):
                return renderer
        return None
    
    def paintEvent(self, event: QPaintEvent) -> None:
        """
        绘制事件处理函数
        
        参数:
            event: QPaintEvent - 绘制事件
        """
        if not self._chart_model:
            return
            
        # 创建画家
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.TextAntialiasing)
        
        # 应用变换（缩放和平移）
        zoom_factor, h_offset, v_offset = self._controller.get_transform_params()
        painter.translate(h_offset, v_offset)
        painter.scale(zoom_factor, zoom_factor)

        # 计算绘制区域
        chart_rect = self.rect()

        # 使用基础单元格尺寸，因为缩放变换已经应用到painter上
        # 这样可以确保符号与网格线完全同步缩放
        scaled_cell_width = self._cell_width
        scaled_cell_height = self._cell_height

        # 执行所有渲染器的绘制
        for renderer in self._renderers:
            if renderer.visible:
                renderer.render(painter, chart_rect, self._chart_model, scaled_cell_width, scaled_cell_height)
        
        # 绘制选择区域
        self._draw_selection(painter)
        
        # 绘制目标线
        self._draw_target_lines(painter)
                
        # 结束绘制
        painter.end()
    
    def _draw_selection(self, painter: QPainter) -> None:
        """
        绘制选择区域
        
        参数:
            painter: QPainter - 画家对象
        """
        if not self._selection:
            return
            
        # 设置选择区域样式
        painter.save()
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(0, 120, 215, 80))  # 半透明蓝色
        
        # 计算选择区域矩形
        start_col = self._selection.get('start_col', 0)
        end_col = self._selection.get('end_col', 0)
        start_price = self._selection.get('start_price', 0.0)
        end_price = self._selection.get('end_price', 0.0)
        
        # 获取价格索引
        price_indices = {}
        if self._chart_model and 'price_data' in self._chart_model:
            prices = self._chart_model['price_data']
            for idx, price in enumerate(prices):
                price_indices[price] = idx
        
        # 计算矩形坐标
        x1 = start_col * self._cell_width
        x2 = (end_col + 1) * self._cell_width
        
        y1 = price_indices.get(start_price, 0) * self._cell_height
        y2 = (price_indices.get(end_price, 0) + 1) * self._cell_height
        
        # 绘制矩形
        painter.drawRect(QRect(x1, y1, x2 - x1, y2 - y1))
        
        painter.restore()
    
    def _draw_target_lines(self, painter: QPainter) -> None:
        """
        绘制目标线
        
        参数:
            painter: QPainter - 画家对象
        """
        if not self._target_lines:
            return
            
        # 设置目标线样式
        painter.save()
        
        # 获取价格索引
        price_indices = {}
        if self._chart_model and 'price_data' in self._chart_model:
            prices = self._chart_model['price_data']
            for idx, price in enumerate(prices):
                price_indices[price] = idx
        
        # 绘制每个目标线
        for target_line in self._target_lines:
            # 设置目标线颜色和样式
            if target_line.is_upward:
                # 上行目标线为蓝色
                painter.setPen(Qt.blue)
            else:
                # 下行目标线为红色
                painter.setPen(Qt.red)
            
            # 计算目标线坐标
            col_count = len(self._chart_model.get('columns', []))
            y = price_indices.get(target_line.target_price, 0) * self._cell_height + self._cell_height / 2
            
            # 绘制水平线
            painter.drawLine(0, y, col_count * self._cell_width, y)
            
            # 绘制目标价格标签
            painter.drawText(col_count * self._cell_width + 5, y + 5, f"{target_line.target_price:.2f}")
            
            # 如果有保守目标，也绘制
            if target_line.min_target is not None:
                y_min = price_indices.get(target_line.min_target, 0) * self._cell_height + self._cell_height / 2
                painter.drawLine(0, y_min, col_count * self._cell_width, y_min)
                painter.drawText(col_count * self._cell_width + 5, y_min + 5, f"{target_line.min_target:.2f}")
        
        painter.restore()
    
    def resizeEvent(self, event: QResizeEvent) -> None:
        """
        大小改变事件处理函数
        
        参数:
            event: QResizeEvent - 大小改变事件
        """
        super().resizeEvent(event)
        self.update()
    
    def event(self, event: QEvent) -> bool:
        """
        事件处理函数
        
        参数:
            event: QEvent - 要处理的事件
            
        返回:
            bool - 事件是否已处理
        """
        # 处理选择区域
        if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
            self._handle_selection_start(event)
        elif event.type() == QEvent.MouseMove and event.buttons() & Qt.LeftButton:
            self._handle_selection_move(event)
        elif event.type() == QEvent.MouseButtonRelease and event.button() == Qt.LeftButton:
            self._handle_selection_end(event)
            
        # 让控制器处理事件
        if hasattr(self, '_controller') and self._controller:
            if self._controller.process_event(event):
                return True
            
        # 调用默认事件处理
        return super().event(event)
    
    def _handle_selection_start(self, event) -> None:
        """
        处理选择开始事件
        
        参数:
            event: QEvent - 鼠标事件
        """
        # 获取鼠标位置
        pos = event.pos()
        
        # 应用逆变换
        zoom_factor, h_offset, v_offset = self._controller.get_transform_params()
        
        # 调整坐标
        adjusted_x = (pos.x() - h_offset) / zoom_factor
        adjusted_y = (pos.y() - v_offset) / zoom_factor
        
        # 计算列索引和价格
        col_idx, price = self.get_cell_at_pos(pos.x(), pos.y())
        
        # 保存选择开始位置
        self._selection_start = {'col': col_idx, 'price': price}
        self._selection_end = None
        self._selection = None
        
        self.update()
    
    def _handle_selection_move(self, event) -> None:
        """
        处理选择移动事件
        
        参数:
            event: QEvent - 鼠标事件
        """
        if not self._selection_start:
            return
            
        # 获取鼠标位置
        pos = event.pos()
        
        # 计算列索引和价格
        col_idx, price = self.get_cell_at_pos(pos.x(), pos.y())
        
        # 更新选择结束位置
        self._selection_end = {'col': col_idx, 'price': price}
        
        # 计算选择区域
        start_col = min(self._selection_start['col'], self._selection_end['col'])
        end_col = max(self._selection_start['col'], self._selection_end['col'])
        start_price = min(self._selection_start['price'], self._selection_end['price'])
        end_price = max(self._selection_start['price'], self._selection_end['price'])
        
        self._selection = {
            'start_col': start_col,
            'end_col': end_col,
            'start_price': start_price,
            'end_price': end_price
        }
        
        self.update()
    
    def _handle_selection_end(self, event) -> None:
        """
        处理选择结束事件
        
        参数:
            event: QEvent - 鼠标事件
        """
        if not self._selection_start or not self._selection_end:
            return
            
        # 选择已完成，不需要额外处理
        pass
    
    def reset_view(self) -> None:
        """重置视图状态"""
        self._controller.reset_view()
        
        # 重置单元格大小
        self._cell_width = self._default_cell_width
        self._cell_height = self._default_cell_height
        
        # 清除选择和目标线
        self._selection = None
        self._selection_start = None
        self._selection_end = None
        self._target_lines = []
        
        self.update()
    
    def set_cell_size(self, width: int, height: int) -> None:
        """
        设置单元格大小
        
        参数:
            width: int - 单元格宽度
            height: int - 单元格高度
        """
        if width > 0 and height > 0:
            self._cell_width = width
            self._cell_height = height
            self.update()
    
    def get_cell_at_pos(self, x: int, y: int) -> Tuple[int, float]:
        """
        获取指定位置对应的单元格坐标
        
        参数:
            x: int - 横坐标
            y: int - 纵坐标
            
        返回:
            Tuple[int, float] - (列索引, 价格)
        """
        if not self._chart_model:
            return (-1, 0.0)
            
        # 应用逆变换
        zoom_factor, h_offset, v_offset = self._controller.get_transform_params()
        
        # 调整坐标
        adjusted_x = (x - h_offset) / zoom_factor
        adjusted_y = (y - v_offset) / zoom_factor
        
        # 计算列索引
        col_idx = int(adjusted_x / self._cell_width)
        
        # 计算价格 (逆向转换y坐标到价格)
        # 假设y轴向下，价格从高到低
        price_idx = int(adjusted_y / self._cell_height)
        if self._chart_model.get('price_data') and len(self._chart_model['price_data']) > 0:
            if price_idx >= 0 and price_idx < len(self._chart_model['price_data']):
                price = self._chart_model['price_data'][price_idx]
            else:
                # 超出范围
                price = 0.0
        else:
            price = 0.0
            
        return (col_idx, price)
    
    def get_selection(self) -> Optional[Dict[str, Any]]:
        """
        获取当前选择区域
        
        返回:
            Optional[Dict[str, Any]] - 选择区域，如果没有选择则返回None
        """
        return self._selection
    
    def show_target_line(self, target_data: TargetPriceData) -> None:
        """
        显示目标线
        
        参数:
            target_data: TargetPriceData - 目标价格数据
        """
        if target_data:
            # 添加到目标线列表
            self._target_lines.append(target_data)
            self.update()
    
    def clear_target_lines(self) -> None:
        """清除所有目标线"""
        self._target_lines = []
        self.update()
    
    def schedule_update(self) -> None:
        """
        调度延迟更新（用于复杂操作中避免频繁重绘）
        """
        # 启动延迟更新定时器
        self._update_timer.start(50)  # 50毫秒后更新 