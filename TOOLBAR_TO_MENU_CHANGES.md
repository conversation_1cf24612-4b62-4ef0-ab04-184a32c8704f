# 工具栏功能移动到菜单栏 - 修改总结

## 修改目标
将点数图界面红框中的所有功能按钮转移到主菜单栏的"工具"菜单中，为点数图表腾挪更多显示空间，提升用户体验。

## 修改文件清单

### 1. src/ui/main_window.py
**修改内容：**
- 在工具菜单中添加了完整的功能菜单结构
- 添加了所有菜单项对应的槽函数
- 添加了DataViewerDialog的导入
- 隐藏了标签页的标签栏

**具体变更：**
```python
# 新增菜单结构
工具菜单
├── 设置
├── 目标位测算快捷键
├── ─────────────────
├── 缩放 ▶
│   ├── 放大 (Ctrl++)
│   ├── 缩小 (Ctrl+-)
│   └── 重置缩放 (Ctrl+0)
├── 目标价测算 ▶
│   ├── 上行目标 (Ctrl+W)
│   ├── 下行目标 (Ctrl+Z)
│   ├── 清除目标线
│   ├── ─────────────
│   └── 目标价位测算对话框
├── 趋势线管理
├── 支撑/阻力位分析
└── 成交量分布图

# 新增槽函数
- _zoom_in()
- _zoom_out()
- _zoom_reset()
- _calculate_upward_target()
- _calculate_downward_target()
- _clear_target_overlays()
- _show_target_price_dialog()
- _manage_trendlines()
- _mark_support_resistance()
- _show_distribution()

# 隐藏标签栏
self.tab_widget.tabBar().setVisible(False)
```

### 2. src/ui/point_figure_chart.py
**修改内容：**
- 移除了工具栏的创建和显示

**具体变更：**
```python
# 注释掉的代码
# self.toolbar = self._create_toolbar()
# self.main_layout.addWidget(self.toolbar)
```

## 功能映射表

| 原工具栏功能 | 新菜单位置 | 快捷键 | 对应方法 |
|-------------|-----------|--------|----------|
| 缩放控制(-/+) | 工具 > 缩放 > 放大/缩小 | Ctrl++/Ctrl+- | `_zoom_in()`, `_zoom_out()` |
| 重置缩放 | 工具 > 缩放 > 重置缩放 | Ctrl+0 | `_zoom_reset()` |
| 上行目标 | 工具 > 目标价测算 > 上行目标 | Ctrl+W | `_calculate_upward_target()` |
| 下行目标 | 工具 > 目标价测算 > 下行目标 | Ctrl+Z | `_calculate_downward_target()` |
| 清除目标线 | 工具 > 目标价测算 > 清除目标线 | - | `_explicit_clear_target_overlays()` |
| 目标价位测算 | 工具 > 目标价测算 > 目标价位测算对话框 | - | `show_target_price_dialog()` |
| 趋势线 | 工具 > 趋势线管理 | - | `manage_trendlines()` |
| 支撑/阻力位 | 工具 > 支撑/阻力位分析 | - | `mark_support_resistance()` |
| 分布图 | 工具 > 成交量分布图 | - | `_on_distribution_button_clicked()` |

## 空间优化效果

1. **移除工具栏**：释放了工具栏占用的垂直空间（约40-50像素）
2. **隐藏标签栏**：释放了标签页标签栏占用的垂直空间（约30像素）
3. **总计释放空间**：约70-80像素的垂直空间，全部用于点数图表显示

## 保持的功能

1. **快捷键功能**：所有原有快捷键（Ctrl+W, Ctrl+Z等）继续有效
2. **完整功能**：所有原工具栏功能都可通过菜单访问
3. **用户体验**：功能组织更加清晰，按类别分组

## 兼容性说明

- 所有现有功能保持不变
- 快捷键操作保持不变
- 数据导入导出功能不受影响
- 设置和配置功能不受影响

## 测试建议

1. 验证所有菜单项都能正确触发对应功能
2. 确认快捷键功能仍然正常工作
3. 测试点数图表显示区域确实增大
4. 验证所有分析功能（目标价测算、趋势线、支撑阻力位等）正常工作
5. 确认成交量分布图功能正常

## 用户使用指南

**原操作方式：** 点击工具栏按钮
**新操作方式：** 通过菜单栏 > 工具菜单访问相应功能

**快捷键操作：** 保持不变，可继续使用原有快捷键

**优势：**
- 更大的图表显示区域
- 更清晰的功能分类
- 更整洁的界面布局 